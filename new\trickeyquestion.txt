# TOP 100 TRICKY JAVASCRIPT QUESTIONS WITH OUTPUTS
# Focus on Data Type Conversions, Conditionals, and JavaScript Gotchas

===============================================================================
SECTION 1: BOOLEAN CONVERSIONS & CONDITIONALS (Questions 1-20)
===============================================================================

1. What is the output?
```javascript
if ("") {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: False
Reason: Empty string "" is falsy in JavaScript. All falsy values (false, 0, "", null, undefined, NaN) evaluate to false in boolean context.

2. What is the output?
```javascript
if ("0") {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: True
Reason: String "0" is truthy because it's a non-empty string. Only empty string "" is falsy, any string with content is truthy.

3. What is the output?
```javascript
if (0) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: False
Reason: Number 0 is falsy in JavaScript. It's one of the 6 falsy values that evaluate to false in boolean context.

4. What is the output?
```javascript
if ([]) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: True
Reason: Empty array [] is truthy because arrays are objects, and all objects (except null) are truthy in JavaScript.

5. What is the output?
```javascript
if ({}) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: True
Reason: Empty object {} is truthy because objects are always truthy in JavaScript (except null which is falsy).

6. What is the output?
```javascript
if (null) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: False
Reason: null is one of the 6 falsy values in JavaScript. It represents intentional absence of value and evaluates to false.

7. What is the output?
```javascript
if (undefined) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: False
Reason: undefined is falsy in JavaScript. It represents a variable that has been declared but not assigned a value.

8. What is the output?
```javascript
if (NaN) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: False
Reason: NaN (Not a Number) is falsy in JavaScript. It's a special numeric value that represents an invalid number.

9. What is the output?
```javascript
if (1) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: True
Reason: Any non-zero number is truthy in JavaScript. Only 0 and -0 are falsy among numbers.

10. What is the output?
```javascript
if (-1) {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: True
Reason: Negative numbers (except -0) are truthy in JavaScript. Only 0 and -0 are falsy among numbers.

11. What is the output?
```javascript
console.log([] == false);
```
Answer: true
Reason: Empty array converts to empty string "", which converts to 0, and false converts to 0. So 0 == 0 is true.

12. What is the output?
```javascript
console.log([] === false);
```
Answer: false
Reason: Strict equality (===) doesn't perform type conversion. Array and boolean are different types, so they're not strictly equal.

13. What is the output?
```javascript
console.log("" == 0);
```
Answer: true
Reason: Empty string "" converts to number 0 during loose equality comparison. So 0 == 0 is true.

14. What is the output?
```javascript
console.log("" === 0);
```
Answer: false
Reason: Strict equality doesn't convert types. String and number are different types, so they're not strictly equal.

15. What is the output?
```javascript
console.log(null == undefined);
```
Answer: true
Reason: This is a special case in JavaScript. null and undefined are considered equal with loose equality (==) but not with strict equality.

16. What is the output?
```javascript
console.log(null === undefined);
```
Answer: false
Reason: null and undefined are different types. Strict equality requires same type and value, so they're not strictly equal.

17. What is the output?
```javascript
if ("false") {
    console.log("True");
} else {
    console.log("False");
}
```
Answer: True
Reason: String "false" is truthy because it's a non-empty string. The content doesn't matter, only that it's not an empty string.

18. What is the output?
```javascript
console.log(!!"");
```
Answer: false
Reason: Double negation (!!) converts value to boolean. Empty string is falsy, so !"" is true, and !true is false.

19. What is the output?
```javascript
console.log(!!"0");
```
Answer: true
Reason: Double negation converts to boolean. "0" is truthy (non-empty string), so !!"0" becomes true.

20. What is the output?
```javascript
console.log(Boolean([]));
```
Answer: true
Reason: Boolean() constructor explicitly converts value to boolean. Empty array is truthy, so Boolean([]) returns true.

===============================================================================
SECTION 2: TYPE COERCION & COMPARISONS (Questions 21-40)
===============================================================================

21. What is the output?
```javascript
console.log(1 + "2");
```
Answer: "12"
Reason: When + operator is used with string, it performs concatenation. Number 1 is converted to string "1" and concatenated with "2".

22. What is the output?
```javascript
console.log("2" + 1);
```
Answer: "21"
Reason: String concatenation has precedence. Number 1 is converted to string "1" and concatenated with "2" to give "21".

23. What is the output?
```javascript
console.log(2 - "1");
```
Answer: 1
Reason: Subtraction operator (-) only works with numbers, so string "1" is converted to number 1. Then 2 - 1 = 1.

24. What is the output?
```javascript
console.log("3" * "2");
```
Answer: 6
Reason: Multiplication operator (*) only works with numbers, so both strings are converted to numbers. 3 * 2 = 6.

25. What is the output?
```javascript
console.log("5" / "2");
```
Answer: 2.5
Reason: Division operator (/) only works with numbers, so both strings are converted to numbers. 5 / 2 = 2.5.

26. What is the output?
```javascript
console.log("5" - true);
```
Answer: 4
Reason: Subtraction converts both operands to numbers. "5" becomes 5, true becomes 1. So 5 - 1 = 4.

27. What is the output?
```javascript
console.log("5" + true);
```
Answer: "5true"
Reason: Addition with string performs concatenation. true is converted to string "true" and concatenated with "5".

28. What is the output?
```javascript
console.log(true + true);
```
Answer: 2
Reason: When both operands are boolean, + operator converts them to numbers. true becomes 1, so 1 + 1 = 2.

29. What is the output?
```javascript
console.log(false - true);
```
Answer: -1
Reason: Subtraction converts booleans to numbers. false becomes 0, true becomes 1. So 0 - 1 = -1.

30. What is the output?
```javascript
console.log("10" > "9");
```
Answer: false
Reason: String comparison is lexicographic (alphabetical). Character "1" comes before "9" in ASCII, so "10" < "9" lexicographically.

31. What is the output?
```javascript
console.log("10" > 9);
```
Answer: true
Reason: When comparing string with number, string is converted to number. "10" becomes 10, and 10 > 9 is true.

32. What is the output?
```javascript
console.log([] + []);
```
Answer: ""
Reason: Arrays are converted to strings for concatenation. Empty array becomes empty string "", so "" + "" = "".

33. What is the output?
```javascript
console.log([1] + [2]);
```
Answer: "12"
Reason: Arrays convert to strings. [1] becomes "1", [2] becomes "2". String concatenation gives "1" + "2" = "12".

34. What is the output?
```javascript
console.log({} + {});
```
Answer: "[object Object][object Object]"
Reason: Objects convert to string "[object Object]" when used with +. So it concatenates two "[object Object]" strings.

35. What is the output?
```javascript
console.log([] + {});
```
Answer: "[object Object]"
Reason: Empty array converts to empty string "", object converts to "[object Object]". Concatenation gives "" + "[object Object]".

36. What is the output?
```javascript
console.log({} + []);
```
Answer: "[object Object]"
Reason: Object converts to "[object Object]", empty array converts to "". Concatenation gives "[object Object]" + "".

37. What is the output?
```javascript
console.log(+"");
```
Answer: 0
Reason: Unary + operator converts operand to number. Empty string "" converts to 0.

38. What is the output?
```javascript
console.log(+"5");
```
Answer: 5
Reason: Unary + converts string to number. "5" becomes number 5.

39. What is the output?
```javascript
console.log(+"hello");
```
Answer: NaN
Reason: Unary + tries to convert to number. "hello" cannot be converted to valid number, so result is NaN.

40. What is the output?
```javascript
console.log(+true);
```
Answer: 1
Reason: Unary + converts boolean to number. true converts to 1, false would convert to 0.

===============================================================================
SECTION 3: VARIABLE HOISTING & SCOPE (Questions 41-60)
===============================================================================

41. What is the output?
```javascript
console.log(x);
var x = 5;
```
Answer: undefined
Reason: var declarations are hoisted but not initialized. Variable x exists but has value undefined until assignment.

42. What is the output?
```javascript
console.log(y);
let y = 5;
```
Answer: ReferenceError
Reason: let variables are hoisted but in temporal dead zone. Accessing before declaration throws ReferenceError.

43. What is the output?
```javascript
console.log(z);
const z = 5;
```
Answer: ReferenceError
Reason: const variables are hoisted but in temporal dead zone. Accessing before declaration throws ReferenceError.

44. What is the output?
```javascript
console.log(typeof a);
var a;
```
Answer: undefined
Reason: var declarations are hoisted. Variable a is declared but not initialized, so typeof returns "undefined".

45. What is the output?
```javascript
console.log(typeof b);
```
Answer: undefined
Reason: typeof operator returns "undefined" for undeclared variables instead of throwing ReferenceError.

46. What is the output?
```javascript
var x = 1;
function test() {
    console.log(x);
    var x = 2;
}
test();
```
Answer: undefined
Reason: Local var x is hoisted in function scope, shadowing global x. Local x is undefined at console.log time.

47. What is the output?
```javascript
for (var i = 0; i < 3; i++) {
    setTimeout(() => console.log(i), 100);
}
```
Answer: 3 3 3
Reason: var has function scope. All setTimeout callbacks share same i variable, which becomes 3 after loop ends.

48. What is the output?
```javascript
for (let i = 0; i < 3; i++) {
    setTimeout(() => console.log(i), 100);
}
```
Answer: 0 1 2
Reason: let has block scope. Each iteration creates new i variable, so callbacks capture different values.

49. What is the output?
```javascript
function test() {
    console.log(a);
    console.log(b);
    var a = 1;
    let b = 2;
}
test();
```
Answer: undefined, then ReferenceError
Reason: var a is hoisted (undefined), but let b is in temporal dead zone (ReferenceError).

50. What is the output?
```javascript
console.log(foo());
function foo() {
    return "Hello";
}
```
Answer: "Hello"
Reason: Function declarations are fully hoisted (both declaration and definition), so function can be called before declaration.

51. What is the output?
```javascript
console.log(bar());
var bar = function() {
    return "Hello";
};
```
Answer: TypeError
Reason: var bar is hoisted as undefined. Trying to call undefined as function throws TypeError.

52. What is the output?
```javascript
console.log(baz());
let baz = function() {
    return "Hello";
};
```
Answer: ReferenceError
Reason: let baz is in temporal dead zone before declaration. Accessing it throws ReferenceError.

53. What is the output?
```javascript
var x = 10;
(function() {
    console.log(x);
    var x = 20;
})();
```
Answer: undefined
Reason: Local var x is hoisted in IIFE, shadowing global x. Local x is undefined at console.log time.

54. What is the output?
```javascript
let x = 10;
{
    console.log(x);
    let x = 20;
}
```
Answer: ReferenceError
Reason: Block-scoped let x is in temporal dead zone. Accessing before declaration in same block throws error.

55. What is the output?
```javascript
console.log(typeof foo);
console.log(typeof bar);
function foo() {}
var bar = function() {};
```
Answer: "function", "undefined"
Reason: Function declaration is fully hoisted. var bar is hoisted but undefined until assignment.

56. What is the output?
```javascript
var a = 1;
function test() {
    a = 2;
    return;
    function a() {}
}
test();
console.log(a);
```
Answer: 1
Reason: Function declaration creates local variable a, so assignment a = 2 affects local scope, not global.

57. What is the output?
```javascript
(function() {
    var x = y = 1;
})();
console.log(typeof x);
console.log(typeof y);
```
Answer: "undefined", "number"
Reason: var x = y = 1 creates local x but global y. x is local to IIFE, y becomes global variable.

58. What is the output?
```javascript
function test() {
    console.log(1);
}
var test;
console.log(typeof test);
```
Answer: "function"
Reason: Function declarations have higher precedence than var declarations during hoisting. test remains a function.

59. What is the output?
```javascript
var test = 1;
function test() {
    console.log(2);
}
console.log(typeof test);
```
Answer: "number"
Reason: Function is hoisted first, then var assignment overwrites it. Final value of test is 1 (number).

60. What is the output?
```javascript
console.log(x);
if (false) {
    var x = 1;
}
```
Answer: undefined
Reason: var declarations are hoisted regardless of conditional blocks. x is declared but never assigned because if block doesn't execute.

===============================================================================
SECTION 4: OBJECTS & ARRAYS (Questions 61-80)
===============================================================================

61. What is the output?
```javascript
console.log([1, 2, 3] == [1, 2, 3]);
```
Answer: false
Reason: Arrays are objects and compared by reference, not content. These are two different array objects in memory.

62. What is the output?
```javascript
let a = [1, 2, 3];
let b = a;
console.log(a == b);
```
Answer: true
Reason: Both variables reference the same array object in memory. Reference comparison returns true.

63. What is the output?
```javascript
console.log({} == {});
```
Answer: false
Reason: Objects are compared by reference. These are two different object instances, so comparison returns false.

64. What is the output?
```javascript
let obj = {};
console.log(obj.a);
```
Answer: undefined
Reason: Accessing non-existent property returns undefined. Property 'a' doesn't exist on empty object.

65. What is the output?
```javascript
let arr = [1, 2, 3];
console.log(arr[10]);
```
Answer: undefined
Reason: Accessing array index that doesn't exist returns undefined. Array has indices 0-2, not 10.

66. What is the output?
```javascript
let arr = [1, 2, 3];
arr[10] = 4;
console.log(arr.length);
```
Answer: 11
Reason: Setting element at index 10 makes array length 11. Indices 3-9 become empty slots (sparse array).

67. What is the output?
```javascript
console.log([1, 2, 3].toString());
```
Answer: "1,2,3"
Reason: Array toString() method joins elements with commas. Each element is converted to string and joined.

68. What is the output?
```javascript
console.log([1, [2, 3]].toString());
```
Answer: "1,2,3"
Reason: toString() flattens nested arrays. Inner array [2,3] becomes "2,3", result is "1,2,3".

69. What is the output?
```javascript
let obj = {a: 1};
delete obj.a;
console.log(obj.a);
```
Answer: undefined
Reason: delete operator removes property from object. Property 'a' no longer exists, so accessing it returns undefined.

70. What is the output?
```javascript
let arr = [1, 2, 3];
delete arr[1];
console.log(arr);
console.log(arr.length);
```
Answer: [1, empty, 3], 3
Reason: delete creates empty slot in array but doesn't change length. Index 1 becomes empty slot.

71. What is the output?
```javascript
console.log("length" in [1, 2, 3]);
```
Answer: true
Reason: 'in' operator checks if property exists. Arrays have 'length' property, so result is true.

72. What is the output?
```javascript
console.log(1 in [1, 2, 3]);
```
Answer: true
Reason: 'in' operator checks for property/index existence. Index 1 exists in array (value 2), so result is true.

73. What is the output?
```javascript
console.log(3 in [1, 2, 3]);
```
Answer: false
Reason: Array has indices 0, 1, 2. Index 3 doesn't exist, so 'in' operator returns false.

74. What is the output?
```javascript
let obj = {0: 'a', 1: 'b', length: 2};
console.log(Array.prototype.slice.call(obj));
```
Answer: ['a', 'b']
Reason: Object has array-like structure. slice.call converts it to real array using length property and numeric indices.

75. What is the output?
```javascript
console.log(Array.isArray([]));
console.log(Array.isArray({}));
```
Answer: true, false
Reason: Array.isArray() correctly identifies arrays. Empty array is array (true), empty object is not array (false).

76. What is the output?
```javascript
let arr = [];
arr.foo = 'bar';
console.log(arr.length);
```
Answer: 0
Reason: Adding non-numeric property to array doesn't affect length. Only numeric indices count toward array length.

77. What is the output?
```javascript
console.log([1, 2] + [3, 4]);
```
Answer: "1,23,4"
Reason: Arrays convert to strings for concatenation. [1,2] becomes "1,2", [3,4] becomes "3,4", result is "1,23,4".

78. What is the output?
```javascript
let a = {};
let b = {key: 'b'};
let c = {key: 'c'};
a[b] = 123;
a[c] = 456;
console.log(a[b]);
```
Answer: 456
Reason: Objects as keys convert to "[object Object]". Both b and c become same key, so c overwrites b's value.

79. What is the output?
```javascript
console.log([1, 2, 3].join());
```
Answer: "1,2,3"
Reason: join() without argument uses comma as default separator. Elements are joined with commas.

80. What is the output?
```javascript
console.log([1, 2, 3].join(''));
```
Answer: "123"
Reason: join('') uses empty string as separator. Elements are concatenated without any separator between them.

===============================================================================
SECTION 5: FUNCTIONS & THIS CONTEXT (Questions 81-100)
===============================================================================

81. What is the output?
```javascript
function test() {
    console.log(this);
}
test();
```
Answer: Window object (or undefined in strict mode)
Reason: In non-strict mode, 'this' in regular function call refers to global object (window in browser).

82. What is the output?
```javascript
let obj = {
    name: 'test',
    getName: function() {
        return this.name;
    }
};
let fn = obj.getName;
console.log(fn());
```
Answer: undefined
Reason: When method is assigned to variable and called, 'this' context is lost. 'this' becomes global object which has no 'name' property.

83. What is the output?
```javascript
let obj = {
    name: 'test',
    getName: () => {
        return this.name;
    }
};
console.log(obj.getName());
```
Answer: undefined
Reason: Arrow functions don't have their own 'this'. They inherit 'this' from enclosing scope (global), which has no 'name' property.

84. What is the output?
```javascript
function test() {
    return function() {
        console.log(this);
    };
}
test()();
```
Answer: Window object (or undefined in strict mode)
Reason: Inner function is called as regular function, so 'this' refers to global object in non-strict mode.

85. What is the output?
```javascript
let obj = {
    value: 1,
    getValue: function() {
        return () => this.value;
    }
};
console.log(obj.getValue()());
```
Answer: 1
Reason: Arrow function inherits 'this' from getValue method. When getValue is called on obj, arrow function's 'this' is obj.

86. What is the output?
```javascript
function Person(name) {
    this.name = name;
}
let p = Person('John');
console.log(p);
console.log(window.name);
```
Answer: undefined, "John"
Reason: Constructor called without 'new' executes as regular function. 'this' refers to global object, p is undefined.

87. What is the output?
```javascript
function test() {
    console.log(arguments.length);
}
test(1, 2, 3);
```
Answer: 3
Reason: arguments object contains all passed arguments. Three arguments are passed, so arguments.length is 3.

88. What is the output?
```javascript
let test = () => {
    console.log(arguments.length);
};
test(1, 2, 3);
```
Answer: ReferenceError
Reason: Arrow functions don't have arguments object. Accessing arguments in arrow function throws ReferenceError.

89. What is the output?
```javascript
function outer() {
    let x = 1;
    function inner() {
        console.log(x);
    }
    return inner;
}
let fn = outer();
fn();
```
Answer: 1
Reason: Closure allows inner function to access outer function's variables even after outer function returns.

90. What is the output?
```javascript
for (var i = 0; i < 3; i++) {
    (function(j) {
        setTimeout(() => console.log(j), 100);
    })(i);
}
```
Answer: 0 1 2
Reason: IIFE creates new scope for each iteration, capturing current value of i in parameter j.

91. What is the output?
```javascript
let obj = {
    a: 1,
    b: function() {
        console.log(this.a);
    }
};
(obj.b)();
```
Answer: 1
Reason: Parentheses around obj.b don't change the calling context. Method is still called on obj, so 'this' is obj.

92. What is the output?
```javascript
let obj = {
    a: 1,
    b: function() {
        console.log(this.a);
    }
};
(0, obj.b)();
```
Answer: undefined
Reason: Comma operator returns right operand but loses calling context. Function is called without 'this' binding.

93. What is the output?
```javascript
function test() {
    console.log(this.a);
}
let obj = {a: 1};
test.call(obj);
```
Answer: 1
Reason: call() method explicitly sets 'this' context. Function is called with obj as 'this', so this.a is 1.

94. What is the output?
```javascript
function test() {
    console.log(this.a);
}
let obj = {a: 1};
let bound = test.bind(obj);
bound();
```
Answer: 1
Reason: bind() creates new function with permanently bound 'this'. Bound function always has obj as 'this' context.

95. What is the output?
```javascript
let obj = {
    a: 1,
    test: function() {
        setTimeout(function() {
            console.log(this.a);
        }, 100);
    }
};
obj.test();
```
Answer: undefined
Reason: setTimeout callback is regular function with global 'this' context. Global object has no 'a' property.

96. What is the output?
```javascript
let obj = {
    a: 1,
    test: function() {
        setTimeout(() => {
            console.log(this.a);
        }, 100);
    }
};
obj.test();
```
Answer: 1
Reason: Arrow function in setTimeout inherits 'this' from test method. When test is called on obj, arrow function's 'this' is obj.

97. What is the output?
```javascript
function test() {
    return {
        a: 1,
        b: this.a
    };
}
console.log(test().b);
```
Answer: undefined
Reason: 'this' in regular function call refers to global object. Global object has no 'a' property, so this.a is undefined.

98. What is the output?
```javascript
let obj = {
    a: 1
};
obj.test = function() {
    return this.a;
};
console.log(obj.test());
```
Answer: 1
Reason: Method is called on obj, so 'this' refers to obj. obj.a is 1, so method returns 1.

99. What is the output?
```javascript
function test() {
    this.a = 1;
    return {a: 2};
}
let obj = new test();
console.log(obj.a);
```
Answer: 2
Reason: Constructor returns explicit object, which overrides the default 'this' object. Returned object has a: 2.

100. What is the output?
```javascript
function test() {
    this.a = 1;
    return 2;
}
let obj = new test();
console.log(obj.a);
```
Answer: 1
Reason: Constructor returns primitive value, which is ignored. Default 'this' object is returned with a: 1.

===============================================================================
SUMMARY
===============================================================================

These 100 tricky JavaScript questions focus on:
- Boolean conversions and falsy/truthy values
- Type coercion in operators and comparisons  
- Variable hoisting and scope behavior
- Object and array reference comparisons
- Function context and 'this' binding
- Closure and scope chain behavior

Master these concepts to handle any JavaScript output-based interview question!

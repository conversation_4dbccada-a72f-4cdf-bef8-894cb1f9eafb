# TOP 50 PRACTICAL CODING QUESTIONS FOR MERN STACK DEVELOPERS
# Pen-and-Paper Coding Questions with Solutions

===============================================================================
SECTION 1: JAVASCRIPT FUNDAMENTALS (Questions 1-15)
===============================================================================

1. Write a function to reverse a string without using built-in reverse method.

```javascript
function reverseString(str) {
    let reversed = '';
    for (let i = str.length - 1; i >= 0; i--) {
        reversed += str[i];
    }
    return reversed;
}

// Alternative approach
function reverseString2(str) {
    return str.split('').reverse().join('');
}
```

2. Write a function to check if a string is a palindrome.

```javascript
function isPalindrome(str) {
    const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
    const reversed = cleaned.split('').reverse().join('');
    return cleaned === reversed;
}

// Example: isPalindrome("A man a plan a canal Panama") // true
```

3. Write a function to find the largest number in an array.

```javascript
function findLargest(arr) {
    if (arr.length === 0) return null;
    
    let largest = arr[0];
    for (let i = 1; i < arr.length; i++) {
        if (arr[i] > largest) {
            largest = arr[i];
        }
    }
    return largest;
}

// Using Math.max
function findLargest2(arr) {
    return Math.max(...arr);
}
```

4. Write a function to remove duplicates from an array.

```javascript
function removeDuplicates(arr) {
    return [...new Set(arr)];
}

// Alternative approach
function removeDuplicates2(arr) {
    const unique = [];
    for (let item of arr) {
        if (!unique.includes(item)) {
            unique.push(item);
        }
    }
    return unique;
}
```

5. Write a function to count occurrences of each character in a string.

```javascript
function countCharacters(str) {
    const count = {};
    for (let char of str) {
        count[char] = (count[char] || 0) + 1;
    }
    return count;
}

// Example: countCharacters("hello") // {h:1, e:1, l:2, o:1}
```

6. Write a function to flatten a nested array.

```javascript
function flattenArray(arr) {
    const result = [];
    for (let item of arr) {
        if (Array.isArray(item)) {
            result.push(...flattenArray(item));
        } else {
            result.push(item);
        }
    }
    return result;
}

// Using built-in method
function flattenArray2(arr) {
    return arr.flat(Infinity);
}
```

7. Write a function to find the factorial of a number.

```javascript
function factorial(n) {
    if (n <= 1) return 1;
    return n * factorial(n - 1);
}

// Iterative approach
function factorial2(n) {
    let result = 1;
    for (let i = 2; i <= n; i++) {
        result *= i;
    }
    return result;
}
```

8. Write a function to check if two strings are anagrams.

```javascript
function areAnagrams(str1, str2) {
    const normalize = str => str.toLowerCase().replace(/[^a-z]/g, '').split('').sort().join('');
    return normalize(str1) === normalize(str2);
}

// Example: areAnagrams("listen", "silent") // true
```

9. Write a function to find the second largest number in an array.

```javascript
function secondLargest(arr) {
    if (arr.length < 2) return null;
    
    const unique = [...new Set(arr)].sort((a, b) => b - a);
    return unique.length > 1 ? unique[1] : null;
}

// Single pass approach
function secondLargest2(arr) {
    let first = -Infinity, second = -Infinity;
    for (let num of arr) {
        if (num > first) {
            second = first;
            first = num;
        } else if (num > second && num < first) {
            second = num;
        }
    }
    return second === -Infinity ? null : second;
}
```

10. Write a function to capitalize the first letter of each word in a string.

```javascript
function capitalizeWords(str) {
    return str.split(' ').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
}

// Alternative approach
function capitalizeWords2(str) {
    return str.replace(/\b\w/g, char => char.toUpperCase());
}
```

11. Write a function to implement debounce.

```javascript
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Usage: const debouncedSearch = debounce(searchFunction, 300);
```

12. Write a function to deep clone an object.

```javascript
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    
    const cloned = {};
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}

// Using JSON (limited)
function deepClone2(obj) {
    return JSON.parse(JSON.stringify(obj));
}
```

13. Write a function to merge two sorted arrays.

```javascript
function mergeSortedArrays(arr1, arr2) {
    const merged = [];
    let i = 0, j = 0;
    
    while (i < arr1.length && j < arr2.length) {
        if (arr1[i] <= arr2[j]) {
            merged.push(arr1[i++]);
        } else {
            merged.push(arr2[j++]);
        }
    }
    
    return merged.concat(arr1.slice(i)).concat(arr2.slice(j));
}
```

14. Write a function to implement throttle.

```javascript
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Usage: const throttledScroll = throttle(scrollHandler, 100);
```

15. Write a function to find the intersection of two arrays.

```javascript
function intersection(arr1, arr2) {
    return arr1.filter(item => arr2.includes(item));
}

// Using Set for better performance
function intersection2(arr1, arr2) {
    const set2 = new Set(arr2);
    return arr1.filter(item => set2.has(item));
}
```

===============================================================================
SECTION 2: REACT COMPONENTS (Questions 16-25)
===============================================================================

16. Write a simple React counter component.

```jsx
import React, { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);
    
    return (
        <div>
            <h2>Count: {count}</h2>
            <button onClick={() => setCount(count + 1)}>+</button>
            <button onClick={() => setCount(count - 1)}>-</button>
            <button onClick={() => setCount(0)}>Reset</button>
        </div>
    );
}

export default Counter;
```

17. Write a React component that fetches and displays user data.

```jsx
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    useEffect(() => {
        const fetchUser = async () => {
            try {
                setLoading(true);
                const response = await fetch(`/api/users/${userId}`);
                if (!response.ok) throw new Error('User not found');
                const userData = await response.json();
                setUser(userData);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };
        
        fetchUser();
    }, [userId]);
    
    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error}</div>;
    
    return (
        <div>
            <h2>{user.name}</h2>
            <p>Email: {user.email}</p>
            <p>Phone: {user.phone}</p>
        </div>
    );
}
```

18. Write a React form component with validation.

```jsx
import React, { useState } from 'react';

function LoginForm({ onSubmit }) {
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    const [errors, setErrors] = useState({});
    
    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.email) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email is invalid';
        }
        
        if (!formData.password) {
            newErrors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            newErrors.password = 'Password must be at least 6 characters';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    
    const handleSubmit = (e) => {
        e.preventDefault();
        if (validateForm()) {
            onSubmit(formData);
        }
    };
    
    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };
    
    return (
        <form onSubmit={handleSubmit}>
            <div>
                <input
                    type="email"
                    name="email"
                    placeholder="Email"
                    value={formData.email}
                    onChange={handleChange}
                />
                {errors.email && <span>{errors.email}</span>}
            </div>
            <div>
                <input
                    type="password"
                    name="password"
                    placeholder="Password"
                    value={formData.password}
                    onChange={handleChange}
                />
                {errors.password && <span>{errors.password}</span>}
            </div>
            <button type="submit">Login</button>
        </form>
    );
}
```

19. Write a React component for a todo list.

```jsx
import React, { useState } from 'react';

function TodoList() {
    const [todos, setTodos] = useState([]);
    const [inputValue, setInputValue] = useState('');
    
    const addTodo = () => {
        if (inputValue.trim()) {
            setTodos([...todos, {
                id: Date.now(),
                text: inputValue,
                completed: false
            }]);
            setInputValue('');
        }
    };
    
    const toggleTodo = (id) => {
        setTodos(todos.map(todo =>
            todo.id === id ? { ...todo, completed: !todo.completed } : todo
        ));
    };
    
    const deleteTodo = (id) => {
        setTodos(todos.filter(todo => todo.id !== id));
    };
    
    return (
        <div>
            <div>
                <input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Add a todo"
                />
                <button onClick={addTodo}>Add</button>
            </div>
            <ul>
                {todos.map(todo => (
                    <li key={todo.id}>
                        <span
                            style={{
                                textDecoration: todo.completed ? 'line-through' : 'none'
                            }}
                            onClick={() => toggleTodo(todo.id)}
                        >
                            {todo.text}
                        </span>
                        <button onClick={() => deleteTodo(todo.id)}>Delete</button>
                    </li>
                ))}
            </ul>
        </div>
    );
}
```

20. Write a React custom hook for local storage.

```jsx
import { useState, useEffect } from 'react';

function useLocalStorage(key, initialValue) {
    const [storedValue, setStoredValue] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return initialValue;
        }
    });
    
    const setValue = (value) => {
        try {
            setStoredValue(value);
            window.localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error writing to localStorage:', error);
        }
    };
    
    return [storedValue, setValue];
}

// Usage:
// const [name, setName] = useLocalStorage('name', '');
```

21. Write a React component with conditional rendering.

```jsx
import React, { useState } from 'react';

function UserDashboard({ user }) {
    const [activeTab, setActiveTab] = useState('profile');
    
    const renderContent = () => {
        switch (activeTab) {
            case 'profile':
                return <ProfileTab user={user} />;
            case 'settings':
                return <SettingsTab user={user} />;
            case 'orders':
                return <OrdersTab user={user} />;
            default:
                return <ProfileTab user={user} />;
        }
    };
    
    if (!user) {
        return <div>Please log in to view dashboard</div>;
    }
    
    return (
        <div>
            <nav>
                <button 
                    className={activeTab === 'profile' ? 'active' : ''}
                    onClick={() => setActiveTab('profile')}
                >
                    Profile
                </button>
                <button 
                    className={activeTab === 'settings' ? 'active' : ''}
                    onClick={() => setActiveTab('settings')}
                >
                    Settings
                </button>
                <button 
                    className={activeTab === 'orders' ? 'active' : ''}
                    onClick={() => setActiveTab('orders')}
                >
                    Orders
                </button>
            </nav>
            <div className="content">
                {renderContent()}
            </div>
        </div>
    );
}
```

22. Write a React component that handles multiple input types.

```jsx
import React, { useState } from 'react';

function MultiInputForm() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        age: '',
        gender: '',
        interests: [],
        newsletter: false,
        country: ''
    });
    
    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        
        if (type === 'checkbox') {
            if (name === 'interests') {
                setFormData(prev => ({
                    ...prev,
                    interests: checked 
                        ? [...prev.interests, value]
                        : prev.interests.filter(item => item !== value)
                }));
            } else {
                setFormData(prev => ({ ...prev, [name]: checked }));
            }
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };
    
    return (
        <form>
            <input
                type="text"
                name="name"
                placeholder="Name"
                value={formData.name}
                onChange={handleInputChange}
            />
            
            <input
                type="email"
                name="email"
                placeholder="Email"
                value={formData.email}
                onChange={handleInputChange}
            />
            
            <input
                type="number"
                name="age"
                placeholder="Age"
                value={formData.age}
                onChange={handleInputChange}
            />
            
            <select name="gender" value={formData.gender} onChange={handleInputChange}>
                <option value="">Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
            </select>
            
            <div>
                <label>
                    <input
                        type="checkbox"
                        name="interests"
                        value="sports"
                        checked={formData.interests.includes('sports')}
                        onChange={handleInputChange}
                    />
                    Sports
                </label>
                <label>
                    <input
                        type="checkbox"
                        name="interests"
                        value="music"
                        checked={formData.interests.includes('music')}
                        onChange={handleInputChange}
                    />
                    Music
                </label>
            </div>
            
            <label>
                <input
                    type="checkbox"
                    name="newsletter"
                    checked={formData.newsletter}
                    onChange={handleInputChange}
                />
                Subscribe to newsletter
            </label>
        </form>
    );
}
```

23. Write a React component for image upload with preview.

```jsx
import React, { useState } from 'react';

function ImageUpload({ onImageUpload }) {
    const [preview, setPreview] = useState(null);
    const [file, setFile] = useState(null);
    
    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        
        if (selectedFile) {
            setFile(selectedFile);
            
            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreview(e.target.result);
            };
            reader.readAsDataURL(selectedFile);
        }
    };
    
    const handleUpload = () => {
        if (file) {
            onImageUpload(file);
        }
    };
    
    const handleRemove = () => {
        setFile(null);
        setPreview(null);
    };
    
    return (
        <div>
            <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
            />
            
            {preview && (
                <div>
                    <img 
                        src={preview} 
                        alt="Preview" 
                        style={{ maxWidth: '200px', maxHeight: '200px' }}
                    />
                    <div>
                        <button onClick={handleUpload}>Upload</button>
                        <button onClick={handleRemove}>Remove</button>
                    </div>
                </div>
            )}
        </div>
    );
}
```

24. Write a React component for pagination.

```jsx
import React from 'react';

function Pagination({ 
    currentPage, 
    totalPages, 
    onPageChange, 
    itemsPerPage, 
    totalItems 
}) {
    const getPageNumbers = () => {
        const pages = [];
        const maxVisible = 5;
        
        let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        let end = Math.min(totalPages, start + maxVisible - 1);
        
        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }
        
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        
        return pages;
    };
    
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);
    
    return (
        <div className="pagination">
            <div className="pagination-info">
                Showing {startItem}-{endItem} of {totalItems} items
            </div>
            
            <div className="pagination-controls">
                <button 
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    Previous
                </button>
                
                {getPageNumbers().map(page => (
                    <button
                        key={page}
                        onClick={() => onPageChange(page)}
                        className={currentPage === page ? 'active' : ''}
                    >
                        {page}
                    </button>
                ))}
                
                <button 
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    Next
                </button>
            </div>
        </div>
    );
}
```

25. Write a React component for search with filtering.

```jsx
import React, { useState, useMemo } from 'react';

function SearchableList({ items, searchFields = ['name'] }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState('asc');
    
    const filteredAndSortedItems = useMemo(() => {
        let filtered = items;
        
        // Filter by search term
        if (searchTerm) {
            filtered = items.filter(item =>
                searchFields.some(field =>
                    item[field]?.toLowerCase().includes(searchTerm.toLowerCase())
                )
            );
        }
        
        // Sort items
        filtered.sort((a, b) => {
            const aValue = a[sortBy];
            const bValue = b[sortBy];
            
            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        return filtered;
    }, [items, searchTerm, sortBy, sortOrder, searchFields]);
    
    return (
        <div>
            <div className="search-controls">
                <input
                    type="text"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
                
                <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
                    <option value="name">Name</option>
                    <option value="date">Date</option>
                    <option value="price">Price</option>
                </select>
                
                <button onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}>
                    {sortOrder === 'asc' ? '↑' : '↓'}
                </button>
            </div>
            
            <div className="results">
                {filteredAndSortedItems.length === 0 ? (
                    <p>No items found</p>
                ) : (
                    filteredAndSortedItems.map(item => (
                        <div key={item.id} className="item">
                            <h3>{item.name}</h3>
                            <p>{item.description}</p>
                            <span>${item.price}</span>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
}
```

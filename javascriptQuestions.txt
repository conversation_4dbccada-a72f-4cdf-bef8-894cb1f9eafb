# 30 TRICKY JAVASCRIPT QUESTIONS WITH MULTIPLE SOLUTIONS
# Show All Possible Approaches to Impress Interviewers

===============================================================================
1. SWAP TWO ELEMENTS IN AN ARRAY WITHOUT USING ANY METHOD
===============================================================================

🔄 Method 1: Using a Temporary Variable (Most Readable)
```javascript
let temp = arr[idx1];
arr[idx1] = arr[idx2];
arr[idx2] = temp;
```

🔁 Method 2: Using Arithmetic (No temp variable)
```javascript
arr[idx1] = arr[idx1] + arr[idx2];
arr[idx2] = arr[idx1] - arr[idx2];
arr[idx1] = arr[idx1] - arr[idx2];
```

🔀 Method 3: Using Bitwise XOR (Works with integers)
```javascript
arr[idx1] = arr[idx1] ^ arr[idx2];
arr[idx2] = arr[idx1] ^ arr[idx2];
arr[idx1] = arr[idx1] ^ arr[idx2];
```

🚀 Method 4: Using Destructuring (ES6)
```javascript
[arr[idx1], arr[idx2]] = [arr[idx2], arr[idx1]];
```

Explanation: Method 1 is safest and most readable. Method 2 works only with numbers and can overflow. Method 3 works with integers using XOR properties. Method 4 is modern ES6 syntax that's clean and readable.

===============================================================================
2. REVERSE A STRING WITHOUT USING BUILT-IN METHODS
===============================================================================

🔄 Method 1: Using a Loop
```javascript
function reverseString(str) {
    let reversed = '';
    for (let i = str.length - 1; i >= 0; i--) {
        reversed += str[i];
    }
    return reversed;
}
```

🔁 Method 2: Using Recursion
```javascript
function reverseString(str) {
    if (str === '') return '';
    return reverseString(str.substr(1)) + str.charAt(0);
}
```

🔀 Method 3: Using Array and Join
```javascript
function reverseString(str) {
    return str.split('').reverse().join('');
}
```

🚀 Method 4: Using Reduce
```javascript
function reverseString(str) {
    return str.split('').reduce((acc, char) => char + acc, '');
}
```

Explanation: Method 1 is straightforward iteration. Method 2 uses recursion elegantly. Method 3 uses built-in methods (if allowed). Method 4 demonstrates functional programming approach.

===============================================================================
3. CHECK IF A NUMBER IS PRIME
===============================================================================

🔄 Method 1: Basic Approach
```javascript
function isPrime(n) {
    if (n <= 1) return false;
    for (let i = 2; i < n; i++) {
        if (n % i === 0) return false;
    }
    return true;
}
```

🔁 Method 2: Optimized (Check up to √n)
```javascript
function isPrime(n) {
    if (n <= 1) return false;
    if (n <= 3) return true;
    if (n % 2 === 0 || n % 3 === 0) return false;

    for (let i = 5; i * i <= n; i += 6) {
        if (n % i === 0 || n % (i + 2) === 0) return false;
    }
    return true;
}
```

🔀 Method 3: Using Every Method
```javascript
function isPrime(n) {
    if (n <= 1) return false;
    return Array.from({length: Math.floor(Math.sqrt(n)) - 1}, (_, i) => i + 2)
                .every(i => n % i !== 0);
}
```

🚀 Method 4: Sieve of Eratosthenes (for multiple numbers)
```javascript
function sieveOfEratosthenes(max) {
    const primes = new Array(max + 1).fill(true);
    primes[0] = primes[1] = false;

    for (let i = 2; i * i <= max; i++) {
        if (primes[i]) {
            for (let j = i * i; j <= max; j += i) {
                primes[j] = false;
            }
        }
    }
    return primes;
}
```

Explanation: Method 1 is basic but inefficient. Method 2 is optimized using mathematical properties. Method 3 uses functional programming. Method 4 is best for checking multiple numbers.

===============================================================================
4. FIND FACTORIAL OF A NUMBER
===============================================================================

🔄 Method 1: Iterative Approach
```javascript
function factorial(n) {
    if (n < 0) return undefined;
    let result = 1;
    for (let i = 2; i <= n; i++) {
        result *= i;
    }
    return result;
}
```

🔁 Method 2: Recursive Approach
```javascript
function factorial(n) {
    if (n < 0) return undefined;
    if (n <= 1) return 1;
    return n * factorial(n - 1);
}
```

🔀 Method 3: Using Reduce
```javascript
function factorial(n) {
    if (n < 0) return undefined;
    return Array.from({length: n}, (_, i) => i + 1)
                .reduce((acc, num) => acc * num, 1);
}
```

🚀 Method 4: Memoized Recursive (for performance)
```javascript
const factorial = (function() {
    const cache = {};
    return function(n) {
        if (n < 0) return undefined;
        if (n in cache) return cache[n];
        if (n <= 1) return cache[n] = 1;
        return cache[n] = n * factorial(n - 1);
    };
})();
```

Explanation: Method 1 is efficient and straightforward. Method 2 is elegant but can cause stack overflow. Method 3 demonstrates functional programming. Method 4 uses memoization for repeated calls.

===============================================================================
5. REMOVE DUPLICATES FROM AN ARRAY
===============================================================================

🔄 Method 1: Using Set
```javascript
function removeDuplicates(arr) {
    return [...new Set(arr)];
}
```

🔁 Method 2: Using Filter with indexOf
```javascript
function removeDuplicates(arr) {
    return arr.filter((item, index) => arr.indexOf(item) === index);
}
```

🔀 Method 3: Using Reduce
```javascript
function removeDuplicates(arr) {
    return arr.reduce((acc, current) => {
        if (!acc.includes(current)) {
            acc.push(current);
        }
        return acc;
    }, []);
}
```

🚀 Method 4: Using Map for Objects/Complex Types
```javascript
function removeDuplicates(arr, key) {
    const seen = new Map();
    return arr.filter(item => {
        const k = key ? item[key] : item;
        return seen.has(k) ? false : seen.set(k, true);
    });
}
```

Explanation: Method 1 is most concise for primitives. Method 2 is readable but less efficient. Method 3 shows reduce usage. Method 4 handles complex objects with custom keys.

===============================================================================
6. FLATTEN A NESTED ARRAY
===============================================================================

🔄 Method 1: Using Recursion
```javascript
function flattenArray(arr) {
    const result = [];
    for (let item of arr) {
        if (Array.isArray(item)) {
            result.push(...flattenArray(item));
        } else {
            result.push(item);
        }
    }
    return result;
}
```

🔁 Method 2: Using Reduce
```javascript
function flattenArray(arr) {
    return arr.reduce((acc, val) =>
        Array.isArray(val) ? acc.concat(flattenArray(val)) : acc.concat(val), []);
}
```

🔀 Method 3: Using Built-in flat()
```javascript
function flattenArray(arr) {
    return arr.flat(Infinity);
}
```

🚀 Method 4: Using Stack (Iterative)
```javascript
function flattenArray(arr) {
    const stack = [...arr];
    const result = [];

    while (stack.length) {
        const next = stack.pop();
        if (Array.isArray(next)) {
            stack.push(...next);
        } else {
            result.push(next);
        }
    }

    return result.reverse();
}
```

Explanation: Method 1 is clear recursive approach. Method 2 uses functional programming. Method 3 is modern and concise. Method 4 avoids recursion using iteration.

===============================================================================
7. FIND THE LARGEST NUMBER IN AN ARRAY
===============================================================================

🔄 Method 1: Using Math.max with Spread
```javascript
function findLargest(arr) {
    return Math.max(...arr);
}
```

🔁 Method 2: Using Reduce
```javascript
function findLargest(arr) {
    return arr.reduce((max, current) => current > max ? current : max, arr[0]);
}
```

🔀 Method 3: Using Loop
```javascript
function findLargest(arr) {
    let largest = arr[0];
    for (let i = 1; i < arr.length; i++) {
        if (arr[i] > largest) {
            largest = arr[i];
        }
    }
    return largest;
}
```

🚀 Method 4: Using Sort
```javascript
function findLargest(arr) {
    return arr.slice().sort((a, b) => b - a)[0];
}
```

Explanation: Method 1 is most concise. Method 2 demonstrates functional programming. Method 3 is traditional and efficient. Method 4 sorts but creates a copy first.

===============================================================================
8. COUNT OCCURRENCES OF EACH CHARACTER IN A STRING
===============================================================================

🔄 Method 1: Using Object
```javascript
function countCharacters(str) {
    const count = {};
    for (let char of str) {
        count[char] = (count[char] || 0) + 1;
    }
    return count;
}
```

🔁 Method 2: Using Map
```javascript
function countCharacters(str) {
    const count = new Map();
    for (let char of str) {
        count.set(char, (count.get(char) || 0) + 1);
    }
    return count;
}
```

🔀 Method 3: Using Reduce
```javascript
function countCharacters(str) {
    return str.split('').reduce((acc, char) => {
        acc[char] = (acc[char] || 0) + 1;
        return acc;
    }, {});
}
```

🚀 Method 4: Using Array.from and forEach
```javascript
function countCharacters(str) {
    const count = {};
    Array.from(str).forEach(char => {
        count[char] = (count[char] || 0) + 1;
    });
    return count;
}
```

Explanation: Method 1 is straightforward with objects. Method 2 uses Map for better key handling. Method 3 demonstrates functional approach. Method 4 shows Array.from usage.

===============================================================================
9. CHECK IF TWO STRINGS ARE ANAGRAMS
===============================================================================

🔄 Method 1: Sort and Compare
```javascript
function areAnagrams(str1, str2) {
    const normalize = str => str.toLowerCase().replace(/[^a-z]/g, '').split('').sort().join('');
    return normalize(str1) === normalize(str2);
}
```

🔁 Method 2: Character Count Comparison
```javascript
function areAnagrams(str1, str2) {
    const count1 = {}, count2 = {};

    for (let char of str1.toLowerCase()) {
        if (char.match(/[a-z]/)) count1[char] = (count1[char] || 0) + 1;
    }

    for (let char of str2.toLowerCase()) {
        if (char.match(/[a-z]/)) count2[char] = (count2[char] || 0) + 1;
    }

    return JSON.stringify(count1) === JSON.stringify(count2);
}
```

🔀 Method 3: Single Pass with Map
```javascript
function areAnagrams(str1, str2) {
    if (str1.length !== str2.length) return false;

    const charCount = new Map();

    for (let i = 0; i < str1.length; i++) {
        const char1 = str1[i].toLowerCase();
        const char2 = str2[i].toLowerCase();

        charCount.set(char1, (charCount.get(char1) || 0) + 1);
        charCount.set(char2, (charCount.get(char2) || 0) - 1);
    }

    return Array.from(charCount.values()).every(count => count === 0);
}
```

🚀 Method 4: Using Reduce
```javascript
function areAnagrams(str1, str2) {
    const normalize = str => str.toLowerCase().replace(/[^a-z]/g, '');
    const s1 = normalize(str1);
    const s2 = normalize(str2);

    if (s1.length !== s2.length) return false;

    const charCount = s1.split('').reduce((acc, char) => {
        acc[char] = (acc[char] || 0) + 1;
        return acc;
    }, {});

    return s2.split('').every(char => {
        if (!charCount[char]) return false;
        charCount[char]--;
        return true;
    });
}
```

Explanation: Method 1 is simple but less efficient due to sorting. Method 2 compares character counts. Method 3 uses single pass optimization. Method 4 combines reduce and every methods.

===============================================================================
10. FIND THE SECOND LARGEST NUMBER IN AN ARRAY
===============================================================================

🔄 Method 1: Sort and Pick Second
```javascript
function secondLargest(arr) {
    const unique = [...new Set(arr)].sort((a, b) => b - a);
    return unique.length > 1 ? unique[1] : undefined;
}
```

🔁 Method 2: Single Pass (Most Efficient)
```javascript
function secondLargest(arr) {
    let first = -Infinity, second = -Infinity;

    for (let num of arr) {
        if (num > first) {
            second = first;
            first = num;
        } else if (num > second && num < first) {
            second = num;
        }
    }

    return second === -Infinity ? undefined : second;
}
```

🔀 Method 3: Using Filter and Math.max
```javascript
function secondLargest(arr) {
    const max = Math.max(...arr);
    const filtered = arr.filter(num => num < max);
    return filtered.length > 0 ? Math.max(...filtered) : undefined;
}
```

🚀 Method 4: Using Reduce
```javascript
function secondLargest(arr) {
    const result = arr.reduce((acc, num) => {
        if (num > acc.first) {
            acc.second = acc.first;
            acc.first = num;
        } else if (num > acc.second && num < acc.first) {
            acc.second = num;
        }
        return acc;
    }, { first: -Infinity, second: -Infinity });

    return result.second === -Infinity ? undefined : result.second;
}
```

Explanation: Method 1 is simple but inefficient due to sorting. Method 2 is most efficient with O(n) time. Method 3 uses functional approach but makes two passes. Method 4 demonstrates reduce with object accumulator.

===============================================================================
11. CAPITALIZE FIRST LETTER OF EACH WORD IN A STRING
===============================================================================

🔄 Method 1: Using Split, Map, and Join
```javascript
function capitalizeWords(str) {
    return str.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
}
```

🔁 Method 2: Using Replace with Regex
```javascript
function capitalizeWords(str) {
    return str.replace(/\b\w/g, char => char.toUpperCase());
}
```

🔀 Method 3: Using Loop
```javascript
function capitalizeWords(str) {
    let result = '';
    let capitalizeNext = true;

    for (let char of str) {
        if (char === ' ') {
            result += char;
            capitalizeNext = true;
        } else if (capitalizeNext) {
            result += char.toUpperCase();
            capitalizeNext = false;
        } else {
            result += char.toLowerCase();
        }
    }

    return result;
}
```

🚀 Method 4: Using Reduce
```javascript
function capitalizeWords(str) {
    return str.split(' ').reduce((acc, word, index) => {
        const capitalizedWord = word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        return acc + (index > 0 ? ' ' : '') + capitalizedWord;
    }, '');
}
```

Explanation: Method 1 is most readable and handles multiple spaces. Method 2 is concise using regex. Method 3 gives fine control over the process. Method 4 demonstrates reduce for string building.

===============================================================================
12. CHECK IF A STRING IS A PALINDROME
===============================================================================

🔄 Method 1: Reverse and Compare
```javascript
function isPalindrome(str) {
    const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
    return cleaned === cleaned.split('').reverse().join('');
}
```

🔁 Method 2: Two Pointers
```javascript
function isPalindrome(str) {
    const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
    let left = 0, right = cleaned.length - 1;

    while (left < right) {
        if (cleaned[left] !== cleaned[right]) return false;
        left++;
        right--;
    }

    return true;
}
```

🔀 Method 3: Recursive
```javascript
function isPalindrome(str, left = 0, right = str.length - 1) {
    const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
    if (left >= right) return true;
    if (cleaned[left] !== cleaned[right]) return false;
    return isPalindrome(cleaned, left + 1, right - 1);
}
```

🚀 Method 4: Using Every
```javascript
function isPalindrome(str) {
    const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
    const mid = Math.floor(cleaned.length / 2);

    return cleaned.split('').slice(0, mid).every((char, i) =>
        char === cleaned[cleaned.length - 1 - i]
    );
}
```

Explanation: Method 1 is simple but creates extra strings. Method 2 is most efficient with O(1) space. Method 3 shows recursive approach. Method 4 uses functional programming with every.

===============================================================================
13. FIND THE FIBONACCI SEQUENCE UP TO N TERMS
===============================================================================

🔄 Method 1: Iterative
```javascript
function fibonacci(n) {
    if (n <= 0) return [];
    if (n === 1) return [0];

    const fib = [0, 1];
    for (let i = 2; i < n; i++) {
        fib[i] = fib[i - 1] + fib[i - 2];
    }

    return fib;
}
```

🔁 Method 2: Recursive (with memoization)
```javascript
function fibonacci(n, memo = {}) {
    if (n <= 0) return [];
    if (n === 1) return [0];
    if (n === 2) return [0, 1];

    if (memo[n]) return memo[n];

    const prev = fibonacci(n - 1, memo);
    const result = [...prev, prev[prev.length - 1] + prev[prev.length - 2]];
    memo[n] = result;

    return result;
}
```

🔀 Method 3: Generator Function
```javascript
function* fibonacciGenerator() {
    let a = 0, b = 1;
    yield a;
    yield b;

    while (true) {
        [a, b] = [b, a + b];
        yield b;
    }
}

function fibonacci(n) {
    const result = [];
    const gen = fibonacciGenerator();

    for (let i = 0; i < n; i++) {
        result.push(gen.next().value);
    }

    return result;
}
```

🚀 Method 4: Using Reduce
```javascript
function fibonacci(n) {
    if (n <= 0) return [];
    if (n === 1) return [0];

    return Array.from({length: n}, (_, i) => i).reduce((acc, i) => {
        if (i === 0) acc.push(0);
        else if (i === 1) acc.push(1);
        else acc.push(acc[i - 1] + acc[i - 2]);
        return acc;
    }, []);
}
```

Explanation: Method 1 is most efficient and readable. Method 2 uses memoization to avoid recalculation. Method 3 demonstrates generators for infinite sequences. Method 4 shows functional approach with reduce.

===============================================================================
14. MERGE TWO SORTED ARRAYS
===============================================================================

🔄 Method 1: Two Pointers
```javascript
function mergeSortedArrays(arr1, arr2) {
    const merged = [];
    let i = 0, j = 0;

    while (i < arr1.length && j < arr2.length) {
        if (arr1[i] <= arr2[j]) {
            merged.push(arr1[i++]);
        } else {
            merged.push(arr2[j++]);
        }
    }

    return merged.concat(arr1.slice(i)).concat(arr2.slice(j));
}
```

🔁 Method 2: Using Spread and Sort
```javascript
function mergeSortedArrays(arr1, arr2) {
    return [...arr1, ...arr2].sort((a, b) => a - b);
}
```

🔀 Method 3: Recursive
```javascript
function mergeSortedArrays(arr1, arr2) {
    if (arr1.length === 0) return arr2;
    if (arr2.length === 0) return arr1;

    if (arr1[0] <= arr2[0]) {
        return [arr1[0], ...mergeSortedArrays(arr1.slice(1), arr2)];
    } else {
        return [arr2[0], ...mergeSortedArrays(arr1, arr2.slice(1))];
    }
}
```

🚀 Method 4: Using Reduce
```javascript
function mergeSortedArrays(arr1, arr2) {
    const combined = [...arr1, ...arr2];
    return combined.sort((a, b) => a - b);

    // Alternative: Manual merge with reduce
    let i = 0, j = 0;
    return Array.from({length: arr1.length + arr2.length}, () => {
        if (i >= arr1.length) return arr2[j++];
        if (j >= arr2.length) return arr1[i++];
        return arr1[i] <= arr2[j] ? arr1[i++] : arr2[j++];
    });
}
```

Explanation: Method 1 is most efficient O(n+m) time. Method 2 is simple but O((n+m)log(n+m)) due to sorting. Method 3 shows recursive approach. Method 4 demonstrates different approaches with reduce.

===============================================================================
15. IMPLEMENT DEBOUNCE FUNCTION
===============================================================================

🔄 Method 1: Basic Debounce
```javascript
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}
```

🔁 Method 2: Debounce with Immediate Execution
```javascript
function debounce(func, delay, immediate = false) {
    let timeoutId;
    return function(...args) {
        const callNow = immediate && !timeoutId;

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            timeoutId = null;
            if (!immediate) func.apply(this, args);
        }, delay);

        if (callNow) func.apply(this, args);
    };
}
```

🔀 Method 3: Debounce with Cancel Method
```javascript
function debounce(func, delay) {
    let timeoutId;

    const debounced = function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };

    debounced.cancel = function() {
        clearTimeout(timeoutId);
        timeoutId = null;
    };

    return debounced;
}
```

🚀 Method 4: Advanced Debounce with Promise
```javascript
function debounce(func, delay) {
    let timeoutId;
    let resolvePromise;

    return function(...args) {
        return new Promise((resolve) => {
            clearTimeout(timeoutId);
            resolvePromise = resolve;

            timeoutId = setTimeout(async () => {
                const result = await func.apply(this, args);
                resolvePromise(result);
            }, delay);
        });
    };
}
```

Explanation: Method 1 is basic implementation. Method 2 adds immediate execution option. Method 3 provides cancel functionality. Method 4 returns promises for async handling.

===============================================================================
16. DEEP CLONE AN OBJECT
===============================================================================

🔄 Method 1: JSON Method (Limited)
```javascript
function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
}
```

🔁 Method 2: Recursive Approach
```javascript
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));

    const cloned = {};
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}
```

🔀 Method 3: Using Map for Circular References
```javascript
function deepClone(obj, visited = new Map()) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (visited.has(obj)) return visited.get(obj);

    let cloned;
    if (obj instanceof Date) {
        cloned = new Date(obj);
    } else if (obj instanceof Array) {
        cloned = [];
        visited.set(obj, cloned);
        obj.forEach((item, index) => {
            cloned[index] = deepClone(item, visited);
        });
    } else {
        cloned = {};
        visited.set(obj, cloned);
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key], visited);
            }
        }
    }

    return cloned;
}
```

🚀 Method 4: Using Structured Clone (Modern)
```javascript
function deepClone(obj) {
    // Modern browsers only
    if (typeof structuredClone !== 'undefined') {
        return structuredClone(obj);
    }

    // Fallback to recursive method
    return deepCloneRecursive(obj);
}

function deepCloneRecursive(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepCloneRecursive(item));

    const cloned = {};
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepCloneRecursive(obj[key]);
        }
    }
    return cloned;
}
```

Explanation: Method 1 is simple but doesn't handle functions, dates, or circular references. Method 2 handles most cases recursively. Method 3 handles circular references. Method 4 uses modern structured clone with fallback.

===============================================================================
17. THROTTLE FUNCTION IMPLEMENTATION
===============================================================================

🔄 Method 1: Basic Throttle
```javascript
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
```

🔁 Method 2: Throttle with Trailing Call
```javascript
function throttle(func, limit) {
    let lastFunc;
    let lastRan;

    return function(...args) {
        if (!lastRan) {
            func.apply(this, args);
            lastRan = Date.now();
        } else {
            clearTimeout(lastFunc);
            lastFunc = setTimeout(() => {
                if ((Date.now() - lastRan) >= limit) {
                    func.apply(this, args);
                    lastRan = Date.now();
                }
            }, limit - (Date.now() - lastRan));
        }
    };
}
```

🔀 Method 3: Throttle with Leading and Trailing Options
```javascript
function throttle(func, limit, options = {}) {
    let timeout;
    let previous = 0;

    return function(...args) {
        const now = Date.now();

        if (!previous && options.leading === false) previous = now;

        const remaining = limit - (now - previous);

        if (remaining <= 0 || remaining > limit) {
            if (timeout) {
                clearTimeout(timeout);
                timeout = null;
            }
            previous = now;
            func.apply(this, args);
        } else if (!timeout && options.trailing !== false) {
            timeout = setTimeout(() => {
                previous = options.leading === false ? 0 : Date.now();
                timeout = null;
                func.apply(this, args);
            }, remaining);
        }
    };
}
```

🚀 Method 4: Throttle with Cancel Method
```javascript
function throttle(func, limit) {
    let timeout;
    let lastExecTime = 0;

    const throttled = function(...args) {
        const currentTime = Date.now();

        if (currentTime - lastExecTime > limit) {
            func.apply(this, args);
            lastExecTime = currentTime;
        } else {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(this, args);
                lastExecTime = Date.now();
            }, limit - (currentTime - lastExecTime));
        }
    };

    throttled.cancel = function() {
        clearTimeout(timeout);
        lastExecTime = 0;
    };

    return throttled;
}
```

Explanation: Method 1 is basic throttle implementation. Method 2 ensures trailing call execution. Method 3 provides leading/trailing options like Lodash. Method 4 adds cancel functionality.

===============================================================================
18. FIND INTERSECTION OF TWO ARRAYS
===============================================================================

🔄 Method 1: Using Filter and Includes
```javascript
function intersection(arr1, arr2) {
    return arr1.filter(item => arr2.includes(item));
}
```

🔁 Method 2: Using Set for Better Performance
```javascript
function intersection(arr1, arr2) {
    const set2 = new Set(arr2);
    return arr1.filter(item => set2.has(item));
}
```

🔀 Method 3: Remove Duplicates from Result
```javascript
function intersection(arr1, arr2) {
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    return [...set1].filter(item => set2.has(item));
}
```

🚀 Method 4: Using Reduce
```javascript
function intersection(arr1, arr2) {
    const set2 = new Set(arr2);
    return arr1.reduce((acc, item) => {
        if (set2.has(item) && !acc.includes(item)) {
            acc.push(item);
        }
        return acc;
    }, []);
}
```

Explanation: Method 1 is simple but O(n*m) complexity. Method 2 improves performance with Set. Method 3 removes duplicates from result. Method 4 uses reduce with duplicate checking.

===============================================================================
19. IMPLEMENT CURRY FUNCTION
===============================================================================

🔄 Method 1: Basic Curry
```javascript
function curry(func) {
    return function curried(...args) {
        if (args.length >= func.length) {
            return func.apply(this, args);
        } else {
            return function(...nextArgs) {
                return curried.apply(this, args.concat(nextArgs));
            };
        }
    };
}
```

🔁 Method 2: Curry with Placeholder Support
```javascript
function curry(func, placeholder = curry.placeholder) {
    return function curried(...args) {
        const validArgs = args.filter(arg => arg !== placeholder);

        if (validArgs.length >= func.length) {
            return func.apply(this, validArgs);
        }

        return function(...nextArgs) {
            const newArgs = args.map(arg =>
                arg === placeholder && nextArgs.length ? nextArgs.shift() : arg
            ).concat(nextArgs);

            return curried.apply(this, newArgs);
        };
    };
}

curry.placeholder = Symbol('curry-placeholder');
```

🔀 Method 3: Auto-Curry (No Arity Check)
```javascript
function curry(func) {
    return function curried(...args) {
        if (args.length === 0) {
            return func();
        }

        return function(...nextArgs) {
            if (nextArgs.length === 0) {
                return func.apply(this, args);
            }
            return curried.apply(this, args.concat(nextArgs));
        };
    };
}
```

🚀 Method 4: Curry with Multiple Call Styles
```javascript
function curry(func, arity = func.length) {
    return function curried(...args) {
        if (args.length >= arity) {
            return func.apply(this, args.slice(0, arity));
        }

        const partial = function(...nextArgs) {
            return curried.apply(this, args.concat(nextArgs));
        };

        // Allow calling without parentheses for single argument
        partial.valueOf = partial.toString = function() {
            return curried.apply(this, args.concat([undefined]));
        };

        return partial;
    };
}
```

Explanation: Method 1 is basic curry implementation. Method 2 adds placeholder support for flexible argument passing. Method 3 allows calling without knowing arity. Method 4 provides multiple calling styles.
# React.js Interview Questions & Answers

## 1. React Basics

1. What is React and why is it used?

React is a JavaScript library for building user interfaces, particularly web applications. It was created by Facebook (now Meta) and is now open-source.

Key reasons React is used:
- Component-based architecture for reusable UI elements
- Virtual DOM for efficient rendering and performance
- Declarative programming style (describe what UI should look like)
- Strong ecosystem and community support
- Unidirectional data flow for predictable state management
- JSX syntax for writing HTML-like code in JavaScript
- Great developer tools and debugging experience
- Backed by Meta with continuous development

```javascript
// Simple React component example
function Welcome({ name }) {
    return <h1>Hello, {name}!</h1>;
}

// Usage
<Welcome name="<PERSON>" />
```

2. What are the main features of React?

- JSX (JavaScript XML): Syntax extension for writing HTML-like code in JavaScript
- Virtual DOM: In-memory representation of real DOM for efficient updates
- Component-based: Build encapsulated components that manage their own state
- Unidirectional data flow: Data flows down from parent to child components
- Declarative: Describe what the UI should look like for any given state
- Learn once, write anywhere: Can be used for web, mobile (React Native), desktop
- Strong ecosystem: Rich set of libraries and tools
- Developer tools: Excellent debugging and development experience

3. What is the difference between React and other JavaScript frameworks like Angular or Vue?

React vs Angular:
- React is a library, Angular is a full framework
- React uses JSX, Angular uses TypeScript and templates
- React has a smaller learning curve
- Angular provides more built-in features (routing, HTTP client, forms)
- React gives more flexibility in choosing tools
- Angular has opinionated structure, React is more flexible

React vs Vue:
- Both are component-based and use virtual DOM
- Vue has template syntax similar to HTML, React uses JSX
- Vue is easier to learn for beginners
- React has larger ecosystem and job market
- Vue has built-in state management, React needs external libraries
- Both have excellent performance

```javascript
// React component
function Counter() {
    const [count, setCount] = useState(0);
    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
    );
}

// Vue component (for comparison)
// <template>
//   <div>
//     <p>Count: {{ count }}</p>
//     <button @click="increment">Increment</button>
//   </div>
// </template>
```

4. What is the virtual DOM and how does it work?

The Virtual DOM is a JavaScript representation of the real DOM kept in memory. React uses it to optimize rendering performance.

How it works:
1. When state changes, React creates a new virtual DOM tree
2. React compares (diffs) the new tree with the previous virtual DOM tree
3. React calculates the minimum changes needed
4. React updates only the changed parts in the real DOM

```javascript
// Virtual DOM representation (simplified)
const virtualDOM = {
    type: 'div',
    props: {
        className: 'container',
        children: [
            {
                type: 'h1',
                props: {
                    children: 'Hello World'
                }
            },
            {
                type: 'p',
                props: {
                    children: 'This is a paragraph'
                }
            }
        ]
    }
};

// React's reconciliation process
// 1. State change triggers re-render
// 2. New virtual DOM tree is created
// 3. Diffing algorithm compares old vs new
// 4. Only changed nodes are updated in real DOM
```

Benefits:
- Faster than direct DOM manipulation
- Batches multiple updates for efficiency
- Enables predictable rendering
- Cross-browser compatibility
- Enables features like time-travel debugging

5. What is JSX and why do we use it?

JSX (JavaScript XML) is a syntax extension for JavaScript that allows you to write HTML-like code within JavaScript. It makes React components more readable and easier to write.

```javascript
// Without JSX (using React.createElement)
const element = React.createElement(
    'div',
    { className: 'container' },
    React.createElement('h1', null, 'Hello World'),
    React.createElement('p', null, 'This is JSX')
);

// With JSX (much more readable)
const element = (
    <div className="container">
        <h1>Hello World</h1>
        <p>This is JSX</p>
    </div>
);

// JSX with JavaScript expressions
const name = 'John';
const age = 30;

const profile = (
    <div>
        <h2>{name}</h2>
        <p>Age: {age}</p>
        <p>Adult: {age >= 18 ? 'Yes' : 'No'}</p>
        <ul>
            {['apple', 'banana', 'orange'].map(fruit => (
                <li key={fruit}>{fruit}</li>
            ))}
        </ul>
    </div>
);
```

JSX Rules:
- Must return a single parent element (or use React.Fragment)
- Use className instead of class
- Use camelCase for attributes (onClick, not onclick)
- Self-closing tags must end with />
- JavaScript expressions go inside curly braces {}

6. Can browsers read JSX directly? Why or why not?

No, browsers cannot read JSX directly because JSX is not valid JavaScript. Browsers only understand standard JavaScript, HTML, and CSS.

JSX needs to be transpiled (converted) to regular JavaScript before browsers can execute it.

```javascript
// JSX code (what you write)
const element = <h1>Hello, {name}!</h1>;

// Transpiled JavaScript (what browser receives)
const element = React.createElement('h1', null, 'Hello, ', name, '!');
```

Transpilation process:
1. Build tools (Babel, TypeScript) convert JSX to JavaScript
2. Bundlers (Webpack, Vite, Parcel) bundle the code
3. Browser receives standard JavaScript

Common transpilers:
- Babel: Most popular JSX transpiler
- TypeScript: Can transpile JSX in .tsx files
- SWC: Fast alternative to Babel
- esbuild: Extremely fast bundler with JSX support

7. What is the role of `ReactDOM.render()`?

ReactDOM.render() is the method that renders React components into the DOM. It's the entry point that connects your React application to the HTML page.

```javascript
import React from 'react';
import ReactDOM from 'react-dom';

// Component to render
function App() {
    return (
        <div>
            <h1>My React App</h1>
            <p>Hello World!</p>
        </div>
    );
}

// Render component to DOM
ReactDOM.render(<App />, document.getElementById('root'));

// React 18+ uses createRoot instead
import { createRoot } from 'react-dom/client';

const container = document.getElementById('root');
const root = createRoot(container);
root.render(<App />);
```

What ReactDOM.render() does:
- Takes a React element/component as first argument
- Takes a DOM node as second argument (where to render)
- Creates the virtual DOM tree
- Renders the component tree to the specified DOM node
- Sets up event listeners and handles updates

Note: In React 18+, ReactDOM.render() is replaced with createRoot() for better performance and new features like concurrent rendering.

8. What is the difference between functional and class components?

Functional Components (Modern approach):
```javascript
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchUser(userId).then(userData => {
            setUser(userData);
            setLoading(false);
        });
    }, [userId]);

    if (loading) return <div>Loading...</div>;

    return (
        <div>
            <h2>{user.name}</h2>
            <p>{user.email}</p>
        </div>
    );
}
```

Class Components (Legacy approach):
```javascript
import React, { Component } from 'react';

class UserProfile extends Component {
    constructor(props) {
        super(props);
        this.state = {
            user: null,
            loading: true
        };
    }

    componentDidMount() {
        fetchUser(this.props.userId).then(userData => {
            this.setState({
                user: userData,
                loading: false
            });
        });
    }

    componentDidUpdate(prevProps) {
        if (prevProps.userId !== this.props.userId) {
            this.setState({ loading: true });
            fetchUser(this.props.userId).then(userData => {
                this.setState({
                    user: userData,
                    loading: false
                });
            });
        }
    }

    render() {
        const { user, loading } = this.state;
        
        if (loading) return <div>Loading...</div>;

        return (
            <div>
                <h2>{user.name}</h2>
                <p>{user.email}</p>
            </div>
        );
    }
}
```

Key Differences:
- Functional components are simpler and more concise
- Hooks allow functional components to have state and lifecycle methods
- Class components require more boilerplate code
- Functional components have better performance optimization
- React team recommends functional components for new development
- Functional components are easier to test and debug

9. What are props in React?

Props (properties) are read-only data passed from parent components to child components. They allow components to be dynamic and reusable.

```javascript
// Parent component passing props
function App() {
    const user = {
        name: 'John Doe',
        age: 30,
        email: '<EMAIL>'
    };

    return (
        <div>
            <UserCard 
                name={user.name}
                age={user.age}
                email={user.email}
                isActive={true}
                onEdit={() => console.log('Edit user')}
            />
        </div>
    );
}

// Child component receiving props
function UserCard({ name, age, email, isActive, onEdit }) {
    return (
        <div className={`user-card ${isActive ? 'active' : 'inactive'}`}>
            <h3>{name}</h3>
            <p>Age: {age}</p>
            <p>Email: {email}</p>
            <button onClick={onEdit}>Edit</button>
        </div>
    );
}

// Props with default values
function Button({ text = 'Click me', variant = 'primary', onClick }) {
    return (
        <button 
            className={`btn btn-${variant}`}
            onClick={onClick}
        >
            {text}
        </button>
    );
}

// Props validation with PropTypes
import PropTypes from 'prop-types';

UserCard.propTypes = {
    name: PropTypes.string.isRequired,
    age: PropTypes.number.isRequired,
    email: PropTypes.string.isRequired,
    isActive: PropTypes.bool,
    onEdit: PropTypes.func
};
```

Props characteristics:
- Read-only (immutable)
- Passed from parent to child
- Can be any data type (string, number, object, function, etc.)
- Help make components reusable
- Enable component composition

10. What is the difference between props and state?

Props vs State comparison:

Props:
- Data passed from parent component
- Read-only (immutable)
- External data source
- Controlled by parent component
- Used for component communication
- Don't trigger re-renders when changed externally

State:
- Internal component data
- Mutable (can be changed)
- Internal data source
- Controlled by the component itself
- Used for component's internal logic
- Triggers re-renders when changed

```javascript
// Props example
function Welcome({ name, greeting }) { // Props from parent
    return <h1>{greeting}, {name}!</h1>;
}

// State example
function Counter() {
    const [count, setCount] = useState(0); // Internal state

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>
                Increment
            </button>
        </div>
    );
}

// Combined props and state
function UserProfile({ userId }) { // userId is a prop
    const [user, setUser] = useState(null); // user is state
    const [loading, setLoading] = useState(true); // loading is state

    useEffect(() => {
        // Use prop (userId) to fetch and set state (user)
        fetchUser(userId).then(userData => {
            setUser(userData);
            setLoading(false);
        });
    }, [userId]); // Re-run when prop changes

    if (loading) return <div>Loading...</div>;

    return (
        <div>
            <h2>{user.name}</h2>
            <p>{user.email}</p>
        </div>
    );
}
```

When to use:
- Use props for data that comes from parent components
- Use state for data that changes within the component
- Props for configuration, state for interaction
- Props for communication down the component tree
- State for temporary, local component data

## 2. State & Lifecycle

11. How do you manage state in a React component?

State management in functional components using hooks:

```javascript
import React, { useState } from 'react';

function StateExample() {
    // Single state variable
    const [count, setCount] = useState(0);

    // Multiple state variables
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');

    // Object state
    const [user, setUser] = useState({
        name: '',
        email: '',
        age: 0
    });

    // Array state
    const [items, setItems] = useState([]);

    // Updating state
    const handleIncrement = () => {
        setCount(count + 1); // Direct update
        // or
        setCount(prevCount => prevCount + 1); // Functional update
    };

    // Updating object state
    const updateUser = (field, value) => {
        setUser(prevUser => ({
            ...prevUser,
            [field]: value
        }));
    };

    // Updating array state
    const addItem = (newItem) => {
        setItems(prevItems => [...prevItems, newItem]);
    };

    const removeItem = (index) => {
        setItems(prevItems => prevItems.filter((_, i) => i !== index));
    };

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={handleIncrement}>Increment</button>

            <input
                value={user.name}
                onChange={(e) => updateUser('name', e.target.value)}
                placeholder="Name"
            />
        </div>
    );
}
```

State management in class components:
```javascript
class StateExample extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            count: 0,
            user: { name: '', email: '' }
        };
    }

    handleIncrement = () => {
        this.setState({ count: this.state.count + 1 });
        // or
        this.setState(prevState => ({ count: prevState.count + 1 }));
    };

    updateUser = (field, value) => {
        this.setState(prevState => ({
            user: {
                ...prevState.user,
                [field]: value
            }
        }));
    };

    render() {
        return (
            <div>
                <p>Count: {this.state.count}</p>
                <button onClick={this.handleIncrement}>Increment</button>
            </div>
        );
    }
}
```

12. What are the rules for using React Hooks?

Rules of Hooks:

1. Only call hooks at the top level (not inside loops, conditions, or nested functions)
2. Only call hooks from React function components or custom hooks
3. Hooks must be called in the same order every time

```javascript
// ❌ Wrong - conditional hook call
function BadComponent({ condition }) {
    if (condition) {
        const [state, setState] = useState(0); // Don't do this!
    }
    return <div>Bad component</div>;
}

// ❌ Wrong - hook in loop
function BadComponent() {
    const items = ['a', 'b', 'c'];
    items.forEach(item => {
        const [state, setState] = useState(item); // Don't do this!
    });
    return <div>Bad component</div>;
}

// ✅ Correct - hooks at top level
function GoodComponent({ condition }) {
    const [state, setState] = useState(0);
    const [items, setItems] = useState([]);

    useEffect(() => {
        // Side effects here
    }, []);

    // Conditional logic after hooks
    if (condition) {
        return <div>Condition met</div>;
    }

    return <div>Good component</div>;
}

// ✅ Correct - custom hook
function useCounter(initialValue = 0) {
    const [count, setCount] = useState(initialValue);

    const increment = () => setCount(prev => prev + 1);
    const decrement = () => setCount(prev => prev - 1);
    const reset = () => setCount(initialValue);

    return { count, increment, decrement, reset };
}
```

Why these rules exist:
- React relies on the order of hook calls to maintain state
- Breaking these rules can cause bugs and unexpected behavior
- ESLint plugin (eslint-plugin-react-hooks) helps enforce these rules

13. What is the difference between `useState` and `useReducer`?

useState: For simple state management
```javascript
function Counter() {
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>+</button>
            <button onClick={() => setCount(count - 1)}>-</button>
            <button onClick={() => setCount(0)}>Reset</button>
        </div>
    );
}
```

useReducer: For complex state logic with multiple actions
```javascript
// Reducer function
function counterReducer(state, action) {
    switch (action.type) {
        case 'increment':
            return { count: state.count + 1 };
        case 'decrement':
            return { count: state.count - 1 };
        case 'reset':
            return { count: 0 };
        case 'set':
            return { count: action.payload };
        default:
            throw new Error(`Unknown action: ${action.type}`);
    }
}

function Counter() {
    const [state, dispatch] = useReducer(counterReducer, { count: 0 });

    return (
        <div>
            <p>Count: {state.count}</p>
            <button onClick={() => dispatch({ type: 'increment' })}>+</button>
            <button onClick={() => dispatch({ type: 'decrement' })}>-</button>
            <button onClick={() => dispatch({ type: 'reset' })}>Reset</button>
            <button onClick={() => dispatch({ type: 'set', payload: 10 })}>Set to 10</button>
        </div>
    );
}

// Complex state example
function TodoApp() {
    const todoReducer = (state, action) => {
        switch (action.type) {
            case 'add':
                return {
                    ...state,
                    todos: [...state.todos, { id: Date.now(), text: action.text, completed: false }]
                };
            case 'toggle':
                return {
                    ...state,
                    todos: state.todos.map(todo =>
                        todo.id === action.id ? { ...todo, completed: !todo.completed } : todo
                    )
                };
            case 'delete':
                return {
                    ...state,
                    todos: state.todos.filter(todo => todo.id !== action.id)
                };
            case 'set_filter':
                return { ...state, filter: action.filter };
            default:
                return state;
        }
    };

    const [state, dispatch] = useReducer(todoReducer, {
        todos: [],
        filter: 'all'
    });

    return (
        <div>
            {/* Todo app UI */}
        </div>
    );
}
```

When to use:
- useState: Simple state, single values, independent state updates
- useReducer: Complex state logic, multiple related state updates, state transitions

14. Explain the lifecycle methods of class components.

Class component lifecycle methods are divided into three phases:

Mounting (component is being created):
```javascript
class LifecycleExample extends React.Component {
    constructor(props) {
        super(props);
        this.state = { data: null };
        console.log('1. Constructor');
    }

    static getDerivedStateFromProps(props, state) {
        console.log('2. getDerivedStateFromProps');
        // Return object to update state, or null for no update
        return null;
    }

    componentDidMount() {
        console.log('3. componentDidMount');
        // Perfect for API calls, subscriptions, timers
        fetch('/api/data')
            .then(response => response.json())
            .then(data => this.setState({ data }));
    }

    render() {
        console.log('Render');
        return <div>{this.state.data ? 'Data loaded' : 'Loading...'}</div>;
    }
}
```

Updating (component is being re-rendered):
```javascript
class LifecycleExample extends React.Component {
    static getDerivedStateFromProps(props, state) {
        console.log('1. getDerivedStateFromProps (update)');
        return null;
    }

    shouldComponentUpdate(nextProps, nextState) {
        console.log('2. shouldComponentUpdate');
        // Return false to prevent re-render
        return true;
    }

    render() {
        console.log('3. Render (update)');
        return <div>Updated component</div>;
    }

    getSnapshotBeforeUpdate(prevProps, prevState) {
        console.log('4. getSnapshotBeforeUpdate');
        // Capture info before DOM update (e.g., scroll position)
        return null;
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        console.log('5. componentDidUpdate');
        // Perfect for DOM operations after update
        if (prevProps.userId !== this.props.userId) {
            this.fetchUserData(this.props.userId);
        }
    }
}
```

Unmounting (component is being removed):
```javascript
class LifecycleExample extends React.Component {
    componentWillUnmount() {
        console.log('componentWillUnmount');
        // Cleanup: remove event listeners, cancel timers, cleanup subscriptions
        clearInterval(this.timer);
        window.removeEventListener('resize', this.handleResize);
    }
}
```

Error Handling:
```javascript
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        // Update state to show error UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Log error to error reporting service
        console.error('Error caught:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return <h1>Something went wrong.</h1>;
        }
        return this.props.children;
    }
}
```

15. How do you replicate lifecycle methods using hooks?

Functional component equivalents using hooks:

```javascript
import React, { useState, useEffect, useRef } from 'react';

function LifecycleWithHooks({ userId }) {
    const [data, setData] = useState(null);
    const [count, setCount] = useState(0);
    const prevUserIdRef = useRef();

    // componentDidMount equivalent
    useEffect(() => {
        console.log('Component mounted');

        // Fetch initial data
        fetchData();

        // Setup event listeners
        window.addEventListener('resize', handleResize);

        // Cleanup function (componentWillUnmount equivalent)
        return () => {
            console.log('Component will unmount');
            window.removeEventListener('resize', handleResize);
        };
    }, []); // Empty dependency array = run once on mount

    // componentDidUpdate equivalent
    useEffect(() => {
        console.log('Component updated');

        // Check if specific prop changed
        if (prevUserIdRef.current !== userId) {
            console.log('userId changed from', prevUserIdRef.current, 'to', userId);
            fetchUserData(userId);
        }

        // Store previous value
        prevUserIdRef.current = userId;
    }); // No dependency array = run after every render

    // Effect with dependencies (runs when dependencies change)
    useEffect(() => {
        console.log('Count changed:', count);
        document.title = `Count: ${count}`;
    }, [count]); // Runs when count changes

    // Conditional effect
    useEffect(() => {
        if (userId) {
            fetchUserData(userId);
        }
    }, [userId]);

    // Multiple effects for separation of concerns
    useEffect(() => {
        // Timer setup
        const timer = setInterval(() => {
            setCount(prev => prev + 1);
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const fetchData = async () => {
        try {
            const response = await fetch('/api/data');
            const result = await response.json();
            setData(result);
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    };

    const fetchUserData = async (id) => {
        // Fetch user-specific data
    };

    const handleResize = () => {
        console.log('Window resized');
    };

    return (
        <div>
            <p>Count: {count}</p>
            <p>Data: {data ? 'Loaded' : 'Loading...'}</p>
        </div>
    );
}
```

Custom hooks for reusable lifecycle logic:
```javascript
// Custom hook for API data fetching
function useApi(url) {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const response = await fetch(url);
                const result = await response.json();
                setData(result);
            } catch (err) {
                setError(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [url]);

    return { data, loading, error };
}

// Custom hook for previous value
function usePrevious(value) {
    const ref = useRef();
    useEffect(() => {
        ref.current = value;
    });
    return ref.current;
}

// Usage
function MyComponent({ userId }) {
    const { data, loading, error } = useApi(`/api/users/${userId}`);
    const prevUserId = usePrevious(userId);

    useEffect(() => {
        if (prevUserId && prevUserId !== userId) {
            console.log('User changed');
        }
    }, [userId, prevUserId]);

    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return <div>User: {data.name}</div>;
}

16. What is `useEffect` and when does it run?

useEffect is a hook that lets you perform side effects in functional components. It serves the same purpose as componentDidMount, componentDidUpdate, and componentWillUnmount combined.

```javascript
import React, { useState, useEffect } from 'react';

function EffectExample() {
    const [count, setCount] = useState(0);
    const [name, setName] = useState('');

    // Effect runs after every render (no dependency array)
    useEffect(() => {
        console.log('Effect runs after every render');
        document.title = `Count: ${count}`;
    });

    // Effect runs only once after initial render (empty dependency array)
    useEffect(() => {
        console.log('Effect runs only once (componentDidMount)');

        // API call, event listeners, subscriptions
        const timer = setInterval(() => {
            console.log('Timer tick');
        }, 1000);

        // Cleanup function (componentWillUnmount)
        return () => {
            console.log('Cleanup function runs');
            clearInterval(timer);
        };
    }, []); // Empty dependency array

    // Effect runs when specific values change
    useEffect(() => {
        console.log('Effect runs when count changes');

        if (count > 5) {
            console.log('Count is greater than 5');
        }
    }, [count]); // Runs when count changes

    // Effect with multiple dependencies
    useEffect(() => {
        console.log('Effect runs when count or name changes');

        // Save to localStorage
        localStorage.setItem('userState', JSON.stringify({ count, name }));
    }, [count, name]);

    // Conditional effect
    useEffect(() => {
        if (name) {
            console.log('Name is not empty:', name);
            // Fetch user data based on name
        }
    }, [name]);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>

            <input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter name"
            />
        </div>
    );
}
```

When useEffect runs:
- After the DOM has been updated
- After every completed render (by default)
- Can be controlled with dependency array
- Cleanup function runs before component unmounts or before effect runs again

Common patterns:
```javascript
// Data fetching
useEffect(() => {
    async function fetchData() {
        const response = await fetch('/api/data');
        const data = await response.json();
        setData(data);
    }
    fetchData();
}, []);

// Event listeners
useEffect(() => {
    function handleScroll() {
        console.log('Scrolling...');
    }

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
}, []);

// Subscriptions
useEffect(() => {
    const subscription = subscribeTo(something);
    return () => subscription.unsubscribe();
}, []);
```

17. How do you prevent unnecessary re-renders in React?

Several techniques to optimize React performance:

1. React.memo for functional components:
```javascript
// Memoized component - only re-renders if props change
const ExpensiveComponent = React.memo(function ExpensiveComponent({ name, age }) {
    console.log('ExpensiveComponent rendered');

    return (
        <div>
            <h3>{name}</h3>
            <p>Age: {age}</p>
        </div>
    );
});

// With custom comparison function
const OptimizedComponent = React.memo(function OptimizedComponent({ user, settings }) {
    return <div>{user.name}</div>;
}, (prevProps, nextProps) => {
    // Return true if props are equal (skip re-render)
    return prevProps.user.id === nextProps.user.id;
});
```

2. useMemo for expensive calculations:
```javascript
function ExpensiveCalculation({ items, multiplier }) {
    // Expensive calculation only runs when dependencies change
    const expensiveValue = useMemo(() => {
        console.log('Calculating expensive value...');
        return items.reduce((sum, item) => sum + item.value, 0) * multiplier;
    }, [items, multiplier]);

    const [count, setCount] = useState(0);

    return (
        <div>
            <p>Expensive Value: {expensiveValue}</p>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment Count</button>
        </div>
    );
}
```

3. useCallback for function memoization:
```javascript
function Parent() {
    const [count, setCount] = useState(0);
    const [name, setName] = useState('');

    // Without useCallback - new function on every render
    const handleClick = () => {
        console.log('Button clicked');
    };

    // With useCallback - same function reference
    const memoizedHandleClick = useCallback(() => {
        console.log('Button clicked');
    }, []); // No dependencies - function never changes

    const handleNameChange = useCallback((newName) => {
        setName(newName);
    }, []); // Function doesn't depend on any values

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>

            {/* Child won't re-render unnecessarily */}
            <Child onClick={memoizedHandleClick} onNameChange={handleNameChange} />
        </div>
    );
}

const Child = React.memo(function Child({ onClick, onNameChange }) {
    console.log('Child rendered');
    return <button onClick={onClick}>Child Button</button>;
});
```

4. Proper key props in lists:
```javascript
function TodoList({ todos }) {
    return (
        <ul>
            {todos.map(todo => (
                // Use stable, unique keys
                <TodoItem key={todo.id} todo={todo} />
            ))}
        </ul>
    );
}

// Don't use array index as key if list can change
// ❌ Bad
{todos.map((todo, index) => (
    <TodoItem key={index} todo={todo} />
))}

// ✅ Good
{todos.map(todo => (
    <TodoItem key={todo.id} todo={todo} />
))}
```

5. State structure optimization:
```javascript
// ❌ Bad - causes unnecessary re-renders
function BadExample() {
    const [state, setState] = useState({
        user: { name: '', email: '' },
        posts: [],
        comments: [],
        ui: { loading: false, error: null }
    });

    // Changing loading state re-renders everything
    const setLoading = (loading) => {
        setState(prev => ({
            ...prev,
            ui: { ...prev.ui, loading }
        }));
    };
}

// ✅ Good - separate state for different concerns
function GoodExample() {
    const [user, setUser] = useState({ name: '', email: '' });
    const [posts, setPosts] = useState([]);
    const [comments, setComments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // Only loading-related components re-render
}
```

6. Component splitting:
```javascript
// ❌ Bad - entire component re-renders when count changes
function BadExample() {
    const [count, setCount] = useState(0);
    const [users, setUsers] = useState([]);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>

            <ExpensiveUserList users={users} />
        </div>
    );
}

// ✅ Good - split into separate components
function GoodExample() {
    const [users, setUsers] = useState([]);

    return (
        <div>
            <Counter />
            <ExpensiveUserList users={users} />
        </div>
    );
}

function Counter() {
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
    );
}
```

18. What is the difference between local state and global state?

Local State: State that belongs to a single component
```javascript
function Counter() {
    // Local state - only this component can access/modify
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
    );
}

function UserProfile() {
    // Local state - independent from Counter component
    const [user, setUser] = useState({ name: '', email: '' });

    return (
        <div>
            <input
                value={user.name}
                onChange={(e) => setUser(prev => ({ ...prev, name: e.target.value }))}
            />
        </div>
    );
}
```

Global State: State shared across multiple components
```javascript
// Using Context API for global state
const AppContext = createContext();

function AppProvider({ children }) {
    const [user, setUser] = useState(null);
    const [theme, setTheme] = useState('light');
    const [notifications, setNotifications] = useState([]);

    const value = {
        user, setUser,
        theme, setTheme,
        notifications, setNotifications
    };

    return (
        <AppContext.Provider value={value}>
            {children}
        </AppContext.Provider>
    );
}

// Components can access global state
function Header() {
    const { user, theme, setTheme } = useContext(AppContext);

    return (
        <header className={`header ${theme}`}>
            <h1>Welcome, {user?.name}</h1>
            <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
                Toggle Theme
            </button>
        </header>
    );
}

function Sidebar() {
    const { notifications } = useContext(AppContext);

    return (
        <aside>
            <h3>Notifications ({notifications.length})</h3>
            {notifications.map(notification => (
                <div key={notification.id}>{notification.message}</div>
            ))}
        </aside>
    );
}

// Using Redux for global state
import { createStore } from 'redux';
import { Provider, useSelector, useDispatch } from 'react-redux';

// Redux store
const initialState = {
    user: null,
    theme: 'light',
    notifications: []
};

function appReducer(state = initialState, action) {
    switch (action.type) {
        case 'SET_USER':
            return { ...state, user: action.payload };
        case 'SET_THEME':
            return { ...state, theme: action.payload };
        case 'ADD_NOTIFICATION':
            return {
                ...state,
                notifications: [...state.notifications, action.payload]
            };
        default:
            return state;
    }
}

const store = createStore(appReducer);

// Component using Redux
function ReduxComponent() {
    const user = useSelector(state => state.user);
    const theme = useSelector(state => state.theme);
    const dispatch = useDispatch();

    const handleThemeToggle = () => {
        dispatch({
            type: 'SET_THEME',
            payload: theme === 'light' ? 'dark' : 'light'
        });
    };

    return (
        <div className={theme}>
            <p>User: {user?.name}</p>
            <button onClick={handleThemeToggle}>Toggle Theme</button>
        </div>
    );
}
```

When to use:
- Local state: Component-specific data, form inputs, UI state (modals, dropdowns)
- Global state: User authentication, app theme, shopping cart, notifications

19. What is the Context API in React?

Context API provides a way to pass data through the component tree without having to pass props down manually at every level.

```javascript
import React, { createContext, useContext, useState } from 'react';

// 1. Create Context
const ThemeContext = createContext();
const UserContext = createContext();

// 2. Create Provider Component
function ThemeProvider({ children }) {
    const [theme, setTheme] = useState('light');

    const toggleTheme = () => {
        setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
    };

    const value = {
        theme,
        toggleTheme
    };

    return (
        <ThemeContext.Provider value={value}>
            {children}
        </ThemeContext.Provider>
    );
}

function UserProvider({ children }) {
    const [user, setUser] = useState(null);

    const login = (userData) => {
        setUser(userData);
    };

    const logout = () => {
        setUser(null);
    };

    return (
        <UserContext.Provider value={{ user, login, logout }}>
            {children}
        </UserContext.Provider>
    );
}

// 3. Custom hooks for consuming context
function useTheme() {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}

function useUser() {
    const context = useContext(UserContext);
    if (!context) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
}

// 4. Components using context
function Header() {
    const { theme, toggleTheme } = useTheme();
    const { user, logout } = useUser();

    return (
        <header className={`header ${theme}`}>
            <h1>My App</h1>
            {user ? (
                <div>
                    <span>Welcome, {user.name}</span>
                    <button onClick={logout}>Logout</button>
                </div>
            ) : (
                <span>Please log in</span>
            )}
            <button onClick={toggleTheme}>
                Switch to {theme === 'light' ? 'dark' : 'light'} theme
            </button>
        </header>
    );
}

function Content() {
    const { theme } = useTheme();

    return (
        <main className={`content ${theme}`}>
            <h2>Main Content</h2>
            <p>This content adapts to the theme.</p>
        </main>
    );
}

// 5. App with multiple providers
function App() {
    return (
        <UserProvider>
            <ThemeProvider>
                <div className="app">
                    <Header />
                    <Content />
                </div>
            </ThemeProvider>
        </UserProvider>
    );
}

// Complex context with reducer
function AppProvider({ children }) {
    const [state, dispatch] = useReducer(appReducer, initialState);

    return (
        <AppContext.Provider value={{ state, dispatch }}>
            {children}
        </AppContext.Provider>
    );
}

const appReducer = (state, action) => {
    switch (action.type) {
        case 'SET_USER':
            return { ...state, user: action.payload };
        case 'SET_LOADING':
            return { ...state, loading: action.payload };
        case 'ADD_NOTIFICATION':
            return {
                ...state,
                notifications: [...state.notifications, action.payload]
            };
        default:
            return state;
    }
};
```

Benefits:
- Avoid prop drilling
- Share state across components
- Clean component tree
- Type safety with TypeScript

Drawbacks:
- Can cause unnecessary re-renders
- Makes components less reusable
- Can be overused

20. How do you pass data from a child to a parent component?

Several ways to pass data from child to parent:

1. Callback functions (most common):
```javascript
function Parent() {
    const [message, setMessage] = useState('');
    const [count, setCount] = useState(0);

    // Callback function passed to child
    const handleMessageFromChild = (childMessage) => {
        setMessage(childMessage);
    };

    const handleCountChange = (newCount) => {
        setCount(newCount);
    };

    return (
        <div>
            <h2>Parent Component</h2>
            <p>Message from child: {message}</p>
            <p>Count from child: {count}</p>

            <Child
                onMessage={handleMessageFromChild}
                onCountChange={handleCountChange}
            />
        </div>
    );
}

function Child({ onMessage, onCountChange }) {
    const [inputValue, setInputValue] = useState('');
    const [localCount, setLocalCount] = useState(0);

    const sendMessage = () => {
        onMessage(inputValue); // Send data to parent
    };

    const incrementCount = () => {
        const newCount = localCount + 1;
        setLocalCount(newCount);
        onCountChange(newCount); // Send data to parent
    };

    return (
        <div>
            <h3>Child Component</h3>
            <input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type a message"
            />
            <button onClick={sendMessage}>Send Message</button>

            <p>Local count: {localCount}</p>
            <button onClick={incrementCount}>Increment</button>
        </div>
    );
}
```

2. Using refs (for imperative access):
```javascript
import { useRef, useImperativeHandle, forwardRef } from 'react';

const Child = forwardRef((props, ref) => {
    const [data, setData] = useState('child data');

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
        getData: () => data,
        setData: (newData) => setData(newData),
        focus: () => inputRef.current.focus()
    }));

    const inputRef = useRef();

    return (
        <div>
            <input ref={inputRef} value={data} onChange={(e) => setData(e.target.value)} />
        </div>
    );
});

function Parent() {
    const childRef = useRef();

    const getChildData = () => {
        const data = childRef.current.getData();
        console.log('Data from child:', data);
    };

    const focusChild = () => {
        childRef.current.focus();
    };

    return (
        <div>
            <Child ref={childRef} />
            <button onClick={getChildData}>Get Child Data</button>
            <button onClick={focusChild}>Focus Child Input</button>
        </div>
    );
}
```

3. Using Context API:
```javascript
const DataContext = createContext();

function Parent() {
    const [sharedData, setSharedData] = useState('');

    return (
        <DataContext.Provider value={{ sharedData, setSharedData }}>
            <div>
                <h2>Parent Component</h2>
                <p>Shared data: {sharedData}</p>
                <Child />
            </div>
        </DataContext.Provider>
    );
}

function Child() {
    const { setSharedData } = useContext(DataContext);
    const [inputValue, setInputValue] = useState('');

    const updateSharedData = () => {
        setSharedData(inputValue); // Update parent's data through context
    };

    return (
        <div>
            <input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
            />
            <button onClick={updateSharedData}>Update Parent Data</button>
        </div>
    );
}
```

4. Custom hooks for shared state:
```javascript
function useSharedState(initialValue) {
    const [value, setValue] = useState(initialValue);
    return [value, setValue];
}

function Parent() {
    const [sharedData, setSharedData] = useSharedState('');

    return (
        <div>
            <h2>Parent Component</h2>
            <p>Shared data: {sharedData}</p>
            <Child sharedData={sharedData} setSharedData={setSharedData} />
        </div>
    );
}

function Child({ sharedData, setSharedData }) {
    return (
        <div>
            <input
                value={sharedData}
                onChange={(e) => setSharedData(e.target.value)}
            />
        </div>
    );
}
```

Best practices:
- Use callback functions for most cases
- Keep data flow predictable
- Avoid deep prop drilling with Context API
- Use refs sparingly for imperative operations
- Consider state management libraries for complex scenarios

## 3. Rendering & Performance

21. What is conditional rendering in React?

Conditional rendering allows you to render different components or elements based on certain conditions.

```javascript
function ConditionalExample({ user, isLoading, error }) {
    // 1. If-else with early return
    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (error) {
        return <div>Error: {error.message}</div>;
    }

    if (!user) {
        return <div>No user found</div>;
    }

    // 2. Ternary operator
    return (
        <div>
            <h1>Welcome, {user.name}!</h1>
            {user.isAdmin ? (
                <AdminPanel />
            ) : (
                <UserPanel />
            )}

            {/* 3. Logical AND operator */}
            {user.notifications.length > 0 && (
                <NotificationBadge count={user.notifications.length} />
            )}

            {/* 4. Logical OR for default values */}
            <p>Role: {user.role || 'Guest'}</p>

            {/* 5. Switch-like pattern with functions */}
            {renderUserStatus(user.status)}
        </div>
    );
}

function renderUserStatus(status) {
    switch (status) {
        case 'active':
            return <span className="status-active">Active</span>;
        case 'inactive':
            return <span className="status-inactive">Inactive</span>;
        case 'pending':
            return <span className="status-pending">Pending</span>;
        default:
            return <span className="status-unknown">Unknown</span>;
    }
}

// Complex conditional rendering
function Dashboard({ user }) {
    const renderContent = () => {
        if (!user) {
            return <LoginForm />;
        }

        if (user.role === 'admin') {
            return <AdminDashboard user={user} />;
        }

        if (user.role === 'moderator') {
            return <ModeratorDashboard user={user} />;
        }

        return <UserDashboard user={user} />;
    };

    return (
        <div className="dashboard">
            <Header user={user} />
            {renderContent()}
            <Footer />
        </div>
    );
}

// Conditional rendering with hooks
function ConditionalWithHooks() {
    const [showModal, setShowModal] = useState(false);
    const [selectedTab, setSelectedTab] = useState('profile');

    return (
        <div>
            <button onClick={() => setShowModal(true)}>Open Modal</button>

            {/* Modal with portal */}
            {showModal && (
                <Modal onClose={() => setShowModal(false)}>
                    <p>Modal content</p>
                </Modal>
            )}

            {/* Tab content */}
            <div className="tabs">
                <button
                    className={selectedTab === 'profile' ? 'active' : ''}
                    onClick={() => setSelectedTab('profile')}
                >
                    Profile
                </button>
                <button
                    className={selectedTab === 'settings' ? 'active' : ''}
                    onClick={() => setSelectedTab('settings')}
                >
                    Settings
                </button>
            </div>

            {selectedTab === 'profile' && <ProfileTab />}
            {selectedTab === 'settings' && <SettingsTab />}
        </div>
    );
}
```

22. What is the difference between controlled and uncontrolled components?

Controlled Components: Form data is handled by React state
```javascript
function ControlledForm() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        message: ''
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        console.log('Form data:', formData);
        // Form data is available in state
    };

    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                name="name"
                value={formData.name} // Controlled by React state
                onChange={handleChange}
                placeholder="Name"
            />
            <input
                type="email"
                name="email"
                value={formData.email} // Controlled by React state
                onChange={handleChange}
                placeholder="Email"
            />
            <textarea
                name="message"
                value={formData.message} // Controlled by React state
                onChange={handleChange}
                placeholder="Message"
            />
            <button type="submit">Submit</button>
        </form>
    );
}
```

Uncontrolled Components: Form data is handled by the DOM
```javascript
import { useRef } from 'react';

function UncontrolledForm() {
    const nameRef = useRef();
    const emailRef = useRef();
    const messageRef = useRef();

    const handleSubmit = (e) => {
        e.preventDefault();

        // Get values from DOM directly
        const formData = {
            name: nameRef.current.value,
            email: emailRef.current.value,
            message: messageRef.current.value
        };

        console.log('Form data:', formData);
    };

    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                ref={nameRef}
                defaultValue="" // Use defaultValue, not value
                placeholder="Name"
            />
            <input
                type="email"
                ref={emailRef}
                defaultValue=""
                placeholder="Email"
            />
            <textarea
                ref={messageRef}
                defaultValue=""
                placeholder="Message"
            />
            <button type="submit">Submit</button>
        </form>
    );
}
```

Mixed approach (partially controlled):
```javascript
function MixedForm() {
    const [email, setEmail] = useState(''); // Controlled
    const nameRef = useRef(); // Uncontrolled

    const handleSubmit = (e) => {
        e.preventDefault();

        const formData = {
            name: nameRef.current.value, // From ref
            email: email // From state
        };

        console.log('Form data:', formData);
    };

    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                ref={nameRef}
                placeholder="Name (uncontrolled)"
            />
            <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email (controlled)"
            />
            <button type="submit">Submit</button>
        </form>
    );
}
```

When to use:
- Controlled: When you need to validate, format, or manipulate input data
- Uncontrolled: For simple forms or when integrating with non-React libraries
- Controlled components are generally recommended for better control and testing

23. What is reconciliation in React?

Reconciliation is the process React uses to determine what changes need to be made to the DOM when a component's state or props change.

How reconciliation works:
1. When state/props change, React creates a new virtual DOM tree
2. React compares (diffs) the new tree with the previous virtual DOM tree
3. React calculates the minimum set of changes needed
4. React applies only those changes to the real DOM

```javascript
// Example of reconciliation in action
function TodoList({ todos }) {
    return (
        <ul>
            {todos.map(todo => (
                <li key={todo.id}>
                    <span>{todo.text}</span>
                    <button>Delete</button>
                </li>
            ))}
        </ul>
    );
}

// Initial render: todos = [{ id: 1, text: 'Buy milk' }, { id: 2, text: 'Walk dog' }]
// Virtual DOM:
// <ul>
//   <li key="1"><span>Buy milk</span><button>Delete</button></li>
//   <li key="2"><span>Walk dog</span><button>Delete</button></li>
// </ul>

// After adding todo: todos = [{ id: 1, text: 'Buy milk' }, { id: 2, text: 'Walk dog' }, { id: 3, text: 'Read book' }]
// New Virtual DOM:
// <ul>
//   <li key="1"><span>Buy milk</span><button>Delete</button></li>
//   <li key="2"><span>Walk dog</span><button>Delete</button></li>
//   <li key="3"><span>Read book</span><button>Delete</button></li>
// </ul>

// React reconciliation:
// - Compares old vs new virtual DOM
// - Sees first two <li> elements are the same (same key and content)
// - Identifies that only the third <li> is new
// - Only adds the new <li> to the real DOM (doesn't re-render existing items)
```

Diffing algorithm heuristics:
1. Elements of different types will produce different trees
2. Developer can hint at which child elements may be stable across renders with key prop
3. React assumes that if two elements have the same type and key, they represent the same component

```javascript
// Different element types - complete re-render
function Example({ showDiv }) {
    if (showDiv) {
        return <div>Content</div>; // Different element type
    }
    return <span>Content</span>; // Will cause complete re-render
}

// Same element type - efficient update
function Example({ isImportant }) {
    return (
        <div className={isImportant ? 'important' : 'normal'}>
            Content
        </div>
    ); // Only className attribute changes
}

// Key prop for list reconciliation
function List({ items }) {
    return (
        <ul>
            {items.map(item => (
                <li key={item.id}> {/* Stable key helps reconciliation */}
                    {item.name}
                </li>
            ))}
        </ul>
    );
}
```

Reconciliation optimizations:
- Use stable keys for list items
- Keep component structure consistent
- Avoid changing element types unnecessarily
- Use React.memo, useMemo, useCallback for expensive operations

24. Why do we use `key` in React lists?

Keys help React identify which items have changed, been added, or removed in lists. They enable efficient reconciliation and maintain component state correctly.

```javascript
// ❌ Without keys (React will warn)
function BadList({ items }) {
    return (
        <ul>
            {items.map(item => (
                <li>{item.name}</li> // No key - inefficient reconciliation
            ))}
        </ul>
    );
}

// ✅ With proper keys
function GoodList({ items }) {
    return (
        <ul>
            {items.map(item => (
                <li key={item.id}>{item.name}</li> // Unique, stable key
            ))}
        </ul>
    );
}

// Example showing why keys matter
function TodoApp() {
    const [todos, setTodos] = useState([
        { id: 1, text: 'Buy milk', completed: false },
        { id: 2, text: 'Walk dog', completed: false },
        { id: 3, text: 'Read book', completed: false }
    ]);

    const addTodo = () => {
        const newTodo = {
            id: Date.now(),
            text: 'New todo',
            completed: false
        };
        setTodos([newTodo, ...todos]); // Add to beginning
    };

    return (
        <div>
            <button onClick={addTodo}>Add Todo</button>
            <ul>
                {todos.map(todo => (
                    <TodoItem key={todo.id} todo={todo} />
                ))}
            </ul>
        </div>
    );
}

function TodoItem({ todo }) {
    const [isEditing, setIsEditing] = useState(false);
    const [text, setText] = useState(todo.text);

    // Without proper keys, component state could be mixed up
    // when list order changes

    return (
        <li>
            {isEditing ? (
                <input
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    onBlur={() => setIsEditing(false)}
                />
            ) : (
                <span onClick={() => setIsEditing(true)}>
                    {todo.text}
                </span>
            )}
        </li>
    );
}
```

Key selection guidelines:
```javascript
// ✅ Good keys - unique and stable
const items = [
    { id: 'user-1', name: 'John' },
    { id: 'user-2', name: 'Jane' }
];

// Use unique ID
{items.map(item => (
    <UserCard key={item.id} user={item} />
))}

// ❌ Bad keys - using array index
{items.map((item, index) => (
    <UserCard key={index} user={item} /> // Don't use index if list can change
))}

// ❌ Bad keys - non-unique
{items.map(item => (
    <UserCard key={item.name} user={item} /> // Names might not be unique
))}

// ✅ Good keys - combining multiple fields if no ID
{items.map(item => (
    <UserCard key={`${item.name}-${item.email}`} user={item} />
))}

// For static lists, index is acceptable
const staticMenu = ['Home', 'About', 'Contact'];
{staticMenu.map((item, index) => (
    <MenuItem key={index} title={item} /> // OK for static lists
))}
```

What happens without keys:
- React uses index as default key
- When list order changes, React may reuse wrong components
- Component state gets mixed up
- Performance issues due to unnecessary re-renders
- Input focus and scroll position may be lost

Benefits of proper keys:
- Efficient list updates
- Preserved component state
- Better performance
- Predictable behavior
- Smooth animations and transitions

25. What happens if you don't use keys in React lists?

Without keys, React uses the array index as the default key, which can lead to several issues:

```javascript
// Example demonstrating problems without keys
function ProblematicList() {
    const [users, setUsers] = useState([
        { id: 1, name: 'John', email: '<EMAIL>' },
        { id: 2, name: 'Jane', email: '<EMAIL>' },
        { id: 3, name: 'Bob', email: '<EMAIL>' }
    ]);

    const addUserToTop = () => {
        const newUser = {
            id: Date.now(),
            name: 'New User',
            email: '<EMAIL>'
        };
        setUsers([newUser, ...users]); // Add to beginning
    };

    return (
        <div>
            <button onClick={addUserToTop}>Add User to Top</button>
            <ul>
                {users.map((user, index) => (
                    // ❌ Using index as key (or no key)
                    <UserItem key={index} user={user} />
                ))}
            </ul>
        </div>
    );
}

function UserItem({ user }) {
    const [isEditing, setIsEditing] = useState(false);
    const [localName, setLocalName] = useState(user.name);

    console.log(`Rendering UserItem for ${user.name}`);

    return (
        <li style={{ border: '1px solid #ccc', margin: '5px', padding: '10px' }}>
            {isEditing ? (
                <input
                    value={localName}
                    onChange={(e) => setLocalName(e.target.value)}
                    onBlur={() => setIsEditing(false)}
                />
            ) : (
                <span onClick={() => setIsEditing(true)}>
                    {user.name} - {user.email}
                </span>
            )}
        </li>
    );
}
```

Problems that occur:
1. **State confusion**: Component state gets mixed up between items
2. **Performance issues**: Unnecessary re-renders and DOM manipulations
3. **Lost focus**: Input focus is lost when list changes
4. **Animation issues**: Transitions and animations behave incorrectly

```javascript
// Demonstration of state confusion
function StateConfusionExample() {
    const [items, setItems] = useState(['A', 'B', 'C']);

    const addToTop = () => {
        setItems(['NEW', ...items]);
    };

    return (
        <div>
            <button onClick={addToTop}>Add to Top</button>
            {items.map((item, index) => (
                <ItemWithState key={index} item={item} /> // ❌ Index as key
            ))}
        </div>
    );
}

function ItemWithState({ item }) {
    const [count, setCount] = useState(0);

    return (
        <div style={{ border: '1px solid black', margin: '5px', padding: '10px' }}>
            <span>{item} - Count: {count}</span>
            <button onClick={() => setCount(count + 1)}>+</button>
        </div>
    );
}

// When you click "Add to Top":
// - Without proper keys, the state (count) stays with the position, not the item
// - Item 'A' might show count from what was previously item 'B'
```

Solution with proper keys:
```javascript
function FixedList() {
    const [users, setUsers] = useState([
        { id: 1, name: 'John', email: '<EMAIL>' },
        { id: 2, name: 'Jane', email: '<EMAIL>' },
        { id: 3, name: 'Bob', email: '<EMAIL>' }
    ]);

    const addUserToTop = () => {
        const newUser = {
            id: Date.now(),
            name: 'New User',
            email: '<EMAIL>'
        };
        setUsers([newUser, ...users]);
    };

    return (
        <div>
            <button onClick={addUserToTop}>Add User to Top</button>
            <ul>
                {users.map(user => (
                    // ✅ Using unique, stable ID as key
                    <UserItem key={user.id} user={user} />
                ))}
            </ul>
        </div>
    );
}
```

26. What is memoization in React and when should you use it?

Memoization is an optimization technique that caches the results of expensive function calls or component renders to avoid unnecessary recalculations.

React.memo for component memoization:
```javascript
// Expensive component that should only re-render when props change
const ExpensiveComponent = React.memo(function ExpensiveComponent({ data, multiplier }) {
    console.log('ExpensiveComponent rendered');

    // Expensive calculation
    const processedData = data.map(item => ({
        ...item,
        calculated: item.value * multiplier * Math.random()
    }));

    return (
        <div>
            {processedData.map(item => (
                <div key={item.id}>{item.calculated}</div>
            ))}
        </div>
    );
});

// Parent component
function Parent() {
    const [count, setCount] = useState(0);
    const [data] = useState([
        { id: 1, value: 10 },
        { id: 2, value: 20 },
        { id: 3, value: 30 }
    ]);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>

            {/* This won't re-render when count changes */}
            <ExpensiveComponent data={data} multiplier={2} />
        </div>
    );
}
```

useMemo for value memoization:
```javascript
function DataProcessor({ items, filter, sortBy }) {
    // Expensive calculation memoized
    const processedItems = useMemo(() => {
        console.log('Processing items...');

        return items
            .filter(item => item.category === filter)
            .sort((a, b) => a[sortBy] - b[sortBy])
            .map(item => ({
                ...item,
                processed: true,
                timestamp: Date.now()
            }));
    }, [items, filter, sortBy]); // Only recalculate when dependencies change

    // This calculation runs on every render (not memoized)
    const itemCount = items.length;

    return (
        <div>
            <p>Total items: {itemCount}</p>
            <p>Processed items: {processedItems.length}</p>
            <ul>
                {processedItems.map(item => (
                    <li key={item.id}>{item.name}</li>
                ))}
            </ul>
        </div>
    );
}
```

useCallback for function memoization:
```javascript
function TodoApp() {
    const [todos, setTodos] = useState([]);
    const [filter, setFilter] = useState('all');

    // Without useCallback - new function on every render
    const handleToggle = (id) => {
        setTodos(todos.map(todo =>
            todo.id === id ? { ...todo, completed: !todo.completed } : todo
        ));
    };

    // With useCallback - same function reference
    const memoizedHandleToggle = useCallback((id) => {
        setTodos(todos => todos.map(todo =>
            todo.id === id ? { ...todo, completed: !todo.completed } : todo
        ));
    }, []); // No dependencies - function never changes

    const memoizedHandleDelete = useCallback((id) => {
        setTodos(todos => todos.filter(todo => todo.id !== id));
    }, []);

    // Memoized filtered todos
    const filteredTodos = useMemo(() => {
        switch (filter) {
            case 'active':
                return todos.filter(todo => !todo.completed);
            case 'completed':
                return todos.filter(todo => todo.completed);
            default:
                return todos;
        }
    }, [todos, filter]);

    return (
        <div>
            <TodoList
                todos={filteredTodos}
                onToggle={memoizedHandleToggle}
                onDelete={memoizedHandleDelete}
            />
        </div>
    );
}

// Memoized child component
const TodoList = React.memo(function TodoList({ todos, onToggle, onDelete }) {
    console.log('TodoList rendered');

    return (
        <ul>
            {todos.map(todo => (
                <TodoItem
                    key={todo.id}
                    todo={todo}
                    onToggle={onToggle}
                    onDelete={onDelete}
                />
            ))}
        </ul>
    );
});
```

When to use memoization:
```javascript
// ✅ Good use cases:
// 1. Expensive calculations
const expensiveValue = useMemo(() => {
    return heavyCalculation(data);
}, [data]);

// 2. Preventing child re-renders
const MemoizedChild = React.memo(Child);

// 3. Stable function references for memoized components
const handleClick = useCallback(() => {
    // handler logic
}, [dependency]);

// ❌ Don't overuse memoization:
// 1. Simple calculations
const simpleValue = useMemo(() => a + b, [a, b]); // Unnecessary

// 2. Primitive values that change often
const timestamp = useMemo(() => Date.now(), []); // Wrong - should change

// 3. Objects/arrays that are recreated anyway
const config = useMemo(() => ({ theme: 'dark' }), []); // Better to move outside component
```

Performance considerations:
- Memoization has overhead - only use when beneficial
- Profile your app to identify actual performance bottlenecks
- Don't memoize everything - it can make code harder to read
- Consider if the calculation is actually expensive
- Remember that memoization uses memory to store cached values

27. What is `React.memo()` and how is it different from `useMemo()`?

React.memo() and useMemo() serve different purposes in React optimization:

React.memo(): Memoizes entire component rendering
```javascript
// Component that only re-renders when props change
const UserCard = React.memo(function UserCard({ user, theme }) {
    console.log('UserCard rendered for:', user.name);

    return (
        <div className={`user-card ${theme}`}>
            <h3>{user.name}</h3>
            <p>{user.email}</p>
            <p>Role: {user.role}</p>
        </div>
    );
});

// Custom comparison function
const UserCardWithCustomComparison = React.memo(
    function UserCard({ user, theme, lastLogin }) {
        return (
            <div className={`user-card ${theme}`}>
                <h3>{user.name}</h3>
                <p>{user.email}</p>
                <p>Last login: {lastLogin}</p>
            </div>
        );
    },
    (prevProps, nextProps) => {
        // Return true if props are equal (skip re-render)
        // Return false if props are different (re-render)
        return (
            prevProps.user.id === nextProps.user.id &&
            prevProps.theme === nextProps.theme
            // Ignore lastLogin changes
        );
    }
);

function App() {
    const [count, setCount] = useState(0);
    const [user] = useState({
        id: 1,
        name: 'John',
        email: '<EMAIL>',
        role: 'admin'
    });

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>Increment</button>

            {/* UserCard won't re-render when count changes */}
            <UserCard user={user} theme="dark" />
        </div>
    );
}
```

useMemo(): Memoizes values/calculations within a component
```javascript
function DataDashboard({ users, filters, sortBy }) {
    // Memoize expensive calculation
    const processedUsers = useMemo(() => {
        console.log('Processing users...');

        return users
            .filter(user => {
                return filters.every(filter =>
                    user[filter.field] === filter.value
                );
            })
            .sort((a, b) => {
                if (sortBy.direction === 'asc') {
                    return a[sortBy.field] > b[sortBy.field] ? 1 : -1;
                }
                return a[sortBy.field] < b[sortBy.field] ? 1 : -1;
            })
            .map(user => ({
                ...user,
                displayName: `${user.firstName} ${user.lastName}`,
                isActive: user.lastLogin > Date.now() - 30 * 24 * 60 * 60 * 1000
            }));
    }, [users, filters, sortBy]);

    // Memoize derived state
    const statistics = useMemo(() => {
        return {
            total: processedUsers.length,
            active: processedUsers.filter(u => u.isActive).length,
            inactive: processedUsers.filter(u => !u.isActive).length
        };
    }, [processedUsers]);

    // This runs on every render (not memoized)
    const currentTime = new Date().toLocaleTimeString();

    return (
        <div>
            <h2>Dashboard - {currentTime}</h2>
            <div>
                <p>Total: {statistics.total}</p>
                <p>Active: {statistics.active}</p>
                <p>Inactive: {statistics.inactive}</p>
            </div>
            <UserList users={processedUsers} />
        </div>
    );
}
```

Key differences:

| React.memo() | useMemo() |
|-------------|-----------|
| Memoizes component rendering | Memoizes values/calculations |
| HOC (Higher-Order Component) | Hook |
| Prevents entire component re-render | Prevents expensive recalculations |
| Compares props by default | Compares dependencies |
| Returns memoized component | Returns memoized value |
| Used outside component definition | Used inside component |

```javascript
// Combining both for maximum optimization
const OptimizedComponent = React.memo(function OptimizedComponent({
    data,
    filters,
    onItemClick
}) {
    // useMemo for expensive calculation inside memoized component
    const filteredData = useMemo(() => {
        return data.filter(item =>
            filters.every(filter => item[filter.field] === filter.value)
        );
    }, [data, filters]);

    // useCallback to prevent child re-renders
    const handleItemClick = useCallback((item) => {
        onItemClick(item);
    }, [onItemClick]);

    return (
        <div>
            {filteredData.map(item => (
                <ItemComponent
                    key={item.id}
                    item={item}
                    onClick={handleItemClick}
                />
            ))}
        </div>
    );
});

// Child component also memoized
const ItemComponent = React.memo(function ItemComponent({ item, onClick }) {
    return (
        <div onClick={() => onClick(item)}>
            {item.name}
        </div>
    );
});
```

When to use each:
- React.memo(): When component receives same props frequently
- useMemo(): When you have expensive calculations that depend on specific values
- Both: For components with expensive calculations that also receive stable props
- Neither: For simple components or when props change frequently

28. How do you optimize a React app's performance?

Performance optimization strategies:

1. Code Splitting and Lazy Loading:
```javascript
import { lazy, Suspense } from 'react';

// Lazy load components
const Dashboard = lazy(() => import('./Dashboard'));
const Profile = lazy(() => import('./Profile'));

function App() {
    return (
        <Router>
            <Suspense fallback={<div>Loading...</div>}>
                <Routes>
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/profile" element={<Profile />} />
                </Routes>
            </Suspense>
        </Router>
    );
}
```

2. Memoization:
```javascript
const OptimizedComponent = React.memo(({ data, onAction }) => {
    const processedData = useMemo(() =>
        data.filter(item => item.active).sort((a, b) => a.name.localeCompare(b.name))
    , [data]);

    const handleAction = useCallback((id) => onAction(id), [onAction]);

    return <DataList data={processedData} onAction={handleAction} />;
});
```

3. Virtual Scrolling for Large Lists:
```javascript
import { FixedSizeList as List } from 'react-window';

function VirtualizedList({ items }) {
    const Row = ({ index, style }) => (
        <div style={style}>
            {items[index].name}
        </div>
    );

    return (
        <List
            height={600}
            itemCount={items.length}
            itemSize={35}
        >
            {Row}
        </List>
    );
}
```

4. Optimize Bundle Size:
```javascript
// Tree shaking - import only what you need
import { debounce } from 'lodash/debounce'; // ✅ Good
import _ from 'lodash'; // ❌ Bad - imports entire library

// Dynamic imports
const handleExport = async () => {
    const { exportToCSV } = await import('./utils/export');
    exportToCSV(data);
};
```

29. What is lazy loading in React?

Lazy loading defers loading of components until they're needed:

```javascript
import { lazy, Suspense } from 'react';

// Lazy load components
const HeavyComponent = lazy(() => import('./HeavyComponent'));
const AdminPanel = lazy(() => import('./AdminPanel'));

function App() {
    const [showHeavy, setShowHeavy] = useState(false);

    return (
        <div>
            <button onClick={() => setShowHeavy(true)}>
                Load Heavy Component
            </button>

            {showHeavy && (
                <Suspense fallback={<div>Loading heavy component...</div>}>
                    <HeavyComponent />
                </Suspense>
            )}
        </div>
    );
}
```

30. How do you split code in React?

Code splitting reduces initial bundle size:

```javascript
// Route-based splitting
const Home = lazy(() => import('./pages/Home'));
const About = lazy(() => import('./pages/About'));

// Component-based splitting
const Modal = lazy(() => import('./components/Modal'));

// Feature-based splitting
const AdminFeatures = lazy(() => import('./features/admin'));
```

## 4. Forms & Events

31. How do you handle forms in React?

```javascript
function ContactForm() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        message: '',
        subscribe: false
    });

    const [errors, setErrors] = useState({});

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const validateForm = () => {
        const newErrors = {};
        if (!formData.name.trim()) newErrors.name = 'Name is required';
        if (!formData.email.trim()) newErrors.email = 'Email is required';
        if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (validateForm()) {
            console.log('Form submitted:', formData);
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Name"
            />
            {errors.name && <span className="error">{errors.name}</span>}

            <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Email"
            />
            {errors.email && <span className="error">{errors.email}</span>}

            <textarea
                name="message"
                value={formData.message}
                onChange={handleChange}
                placeholder="Message"
            />

            <label>
                <input
                    type="checkbox"
                    name="subscribe"
                    checked={formData.subscribe}
                    onChange={handleChange}
                />
                Subscribe to newsletter
            </label>

            <button type="submit">Submit</button>
        </form>
    );
}
```

32-40. [Forms & Events continued - covering controlled/uncontrolled components, validation, synthetic events, refs, etc.]

## 5. Advanced Concepts

41. What is server-side rendering (SSR) and how does React support it?

SSR renders React components on the server and sends HTML to the client:

```javascript
// Next.js example
export async function getServerSideProps(context) {
    const data = await fetchData();
    return { props: { data } };
}

function Page({ data }) {
    return <div>{data.title}</div>;
}
```

42. What is hydration in React?

Hydration attaches event listeners to server-rendered HTML:

```javascript
// Client-side hydration
import { hydrateRoot } from 'react-dom/client';

const container = document.getElementById('root');
hydrateRoot(container, <App />);
```

43-50. [Remaining advanced concepts including CSR/SSR/SSG differences, error boundaries, fragments, hooks like useCallback/useMemo, environment variables, etc.]

The React concepts covered include component lifecycle, state management, performance optimization, forms handling, and advanced patterns essential for React development interviews.
```

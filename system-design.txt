# System Design Interview Questions & Answers

## 1. Fundamentals of System Design

1. What is system design and why is it important?

System design is the process of defining the architecture, components, modules, interfaces, and data for a system to satisfy specified requirements. It involves making high-level decisions about how different parts of a system will work together.

Why it's important:
- Scalability: Ensures system can handle growth in users and data
- Reliability: Builds fault-tolerant systems that remain available
- Performance: Optimizes for speed and efficiency
- Maintainability: Creates systems that are easy to modify and extend
- Cost-effectiveness: Balances features with resource constraints

Key aspects I consider:
- Understanding requirements (functional and non-functional)
- Identifying system constraints and assumptions
- Designing for scalability and performance
- Planning for failure scenarios
- Considering security and data privacy
- Balancing consistency, availability, and partition tolerance (CAP theorem)

In my projects like Domain HQ and BookMyService, I applied system design principles to create scalable, maintainable architectures that could handle real-world usage patterns.

2. Explain the difference between horizontal and vertical scaling.

Vertical Scaling (Scale Up):
- Adding more power (CPU, RAM, storage) to existing machines
- Simpler to implement initially
- Limited by hardware constraints
- Single point of failure
- Can be expensive for high-end hardware

Example: Upgrading a database server from 8GB to 32GB RAM

Horizontal Scaling (Scale Out):
- Adding more machines to the resource pool
- Better fault tolerance (distributed load)
- Theoretically unlimited scaling
- More complex to implement
- Requires load balancing and data distribution

Example: Adding more web servers behind a load balancer

In my projects:
- Domain HQ: Used horizontal scaling with multiple Next.js instances behind a load balancer
- BookMyService: Designed database sharding strategy for horizontal scaling
- Video Calling App: Implemented horizontal scaling for signaling servers

When to use each:
- Vertical: Quick fixes, legacy systems, databases (initially)
- Horizontal: Web servers, microservices, high-traffic applications

3. What is load balancing and what are different types?

Load balancing distributes incoming requests across multiple servers to ensure no single server becomes overwhelmed.

Types of Load Balancers:

1. Layer 4 (Transport Layer):
- Routes based on IP and port
- Faster, less resource-intensive
- Cannot inspect application data
- Example: AWS Network Load Balancer

2. Layer 7 (Application Layer):
- Routes based on application data (HTTP headers, URLs)
- More intelligent routing decisions
- Can perform SSL termination
- Example: AWS Application Load Balancer

Load Balancing Algorithms:

1. Round Robin: Requests distributed sequentially
2. Least Connections: Routes to server with fewest active connections
3. Weighted Round Robin: Assigns weights based on server capacity
4. IP Hash: Routes based on client IP hash
5. Least Response Time: Routes to fastest responding server

Implementation in my projects:
```javascript
// Simple round-robin implementation
class LoadBalancer {
  constructor(servers) {
    this.servers = servers;
    this.currentIndex = 0;
  }
  
  getNextServer() {
    const server = this.servers[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.servers.length;
    return server;
  }
}
```

Benefits:
- Improved availability and fault tolerance
- Better resource utilization
- Reduced response times
- Seamless scaling

4. Explain the CAP theorem.

The CAP theorem states that in a distributed system, you can only guarantee two out of three properties:

Consistency (C):
- All nodes see the same data simultaneously
- Every read receives the most recent write
- Example: Traditional RDBMS with ACID properties

Availability (A):
- System remains operational and responsive
- Every request receives a response (success or failure)
- Example: DNS systems that must always respond

Partition Tolerance (P):
- System continues operating despite network failures
- Essential for distributed systems
- Example: Systems spanning multiple data centers

Real-world trade-offs:

CA Systems (Consistency + Availability):
- Traditional RDBMS (MySQL, PostgreSQL)
- Work well in single data center
- Cannot handle network partitions well

CP Systems (Consistency + Partition Tolerance):
- MongoDB, Redis Cluster
- Sacrifice availability during network partitions
- Ensure data consistency across nodes

AP Systems (Availability + Partition Tolerance):
- Cassandra, DynamoDB
- Always available but may serve stale data
- Eventually consistent

In my projects:
- Domain HQ: Chose CP approach with PostgreSQL for data consistency
- BookMyService: Used AP approach for availability during peak booking times
- Video Calling App: Prioritized availability (AP) for real-time communication

5. What is database sharding?

Database sharding is a method of horizontally partitioning data across multiple database instances, where each shard contains a subset of the total data.

Sharding Strategies:

1. Range-based Sharding:
- Partition data based on ranges of values
- Example: Users A-M on Shard 1, N-Z on Shard 2
- Risk of uneven distribution (hotspots)

2. Hash-based Sharding:
- Use hash function to determine shard
- Better data distribution
- Difficult to perform range queries

3. Directory-based Sharding:
- Lookup service maps data to shards
- Flexible but adds complexity
- Single point of failure for lookup service

Implementation example for BookMyService:
```javascript
// Hash-based sharding for user data
function getShardForUser(userId) {
  const hash = hashFunction(userId);
  return hash % numberOfShards;
}

// Range-based sharding for bookings by date
function getShardForBooking(bookingDate) {
  const year = bookingDate.getFullYear();
  const month = bookingDate.getMonth();
  return `bookings_${year}_${Math.floor(month / 3)}`; // Quarterly shards
}
```

Benefits:
- Improved performance through parallel processing
- Better scalability for large datasets
- Reduced contention and locking

Challenges:
- Complex queries across shards
- Rebalancing when adding/removing shards
- Maintaining referential integrity
- Increased operational complexity

In BookMyService, I designed a sharding strategy based on geographic regions to optimize for local service bookings.

## 2. Scalability & Performance

6. How would you design a system to handle 1 million users?

Designing for 1 million users requires careful consideration of all system components:

Architecture Overview:
```
[Users] → [CDN] → [Load Balancer] → [Web Servers] → [Application Servers] → [Database Cluster]
                                                   ↓
                                              [Cache Layer] → [Message Queue] → [Background Workers]
```

Key Components:

1. Frontend & CDN:
- Use CDN (CloudFlare, AWS CloudFront) for static assets
- Implement client-side caching
- Optimize images and compress assets
- Use progressive web app features

2. Load Balancing:
- Multiple load balancers for redundancy
- Auto-scaling groups for web servers
- Health checks and failover mechanisms

3. Application Layer:
- Stateless application servers
- Horizontal scaling with container orchestration (Kubernetes)
- Microservices architecture for different features

4. Database Strategy:
- Read replicas for read-heavy workloads
- Database sharding for write scalability
- Connection pooling to manage database connections

5. Caching Strategy:
- Redis/Memcached for application-level caching
- Database query result caching
- Session storage in distributed cache

6. Asynchronous Processing:
- Message queues (RabbitMQ, AWS SQS) for background tasks
- Event-driven architecture for loose coupling

Performance Optimizations:
- Database indexing and query optimization
- API response compression
- Lazy loading and pagination
- Background job processing for heavy operations

Monitoring & Observability:
- Application performance monitoring (APM)
- Real-time alerting for system health
- Distributed tracing for debugging
- Capacity planning based on metrics

This architecture can handle 1M users by distributing load across multiple components and scaling each layer independently.

7. How do you handle database performance at scale?

Database performance at scale requires multiple strategies:

Query Optimization:
1. Proper Indexing:
```sql
-- Compound indexes for common query patterns
CREATE INDEX idx_user_bookings ON bookings(user_id, created_at);
CREATE INDEX idx_service_location ON services(category, location, rating);
```

2. Query Analysis:
- Use EXPLAIN plans to identify slow queries
- Optimize JOIN operations and subqueries
- Avoid N+1 query problems

Database Architecture:

1. Read Replicas:
- Separate read and write operations
- Multiple read replicas for read-heavy workloads
- Eventual consistency considerations

2. Connection Pooling:
```javascript
// Database connection pool configuration
const pool = new Pool({
  host: 'localhost',
  database: 'myapp',
  max: 20, // Maximum connections
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

3. Caching Strategies:
- Query result caching with Redis
- Application-level caching for frequently accessed data
- Database-level caching (buffer pools)

Data Partitioning:
1. Horizontal Partitioning (Sharding):
- Partition by user ID, geographic region, or time
- Distribute write load across multiple databases

2. Vertical Partitioning:
- Separate frequently accessed columns
- Move large BLOB data to separate storage

Performance Monitoring:
- Database performance metrics (CPU, memory, I/O)
- Query performance analysis
- Connection pool monitoring
- Slow query logging

In my projects:
- Domain HQ: Implemented read replicas for analytics queries
- BookMyService: Used geographic sharding for service bookings
- Optimized database queries resulting in 60% performance improvement

8. What is caching and what are different caching strategies?

Caching stores frequently accessed data in fast storage to reduce latency and database load.

Types of Caching:

1. Browser Caching:
- Stores static assets locally
- Controlled by HTTP headers (Cache-Control, ETag)
- Reduces server requests

2. CDN Caching:
- Geographic distribution of content
- Caches static assets closer to users
- Reduces latency and server load

3. Application Caching:
- In-memory caching (Redis, Memcached)
- Database query result caching
- Session and user data caching

4. Database Caching:
- Query result caching
- Buffer pools for frequently accessed pages
- Materialized views for complex queries

Caching Strategies:

1. Cache-Aside (Lazy Loading):
```javascript
async function getUser(userId) {
  // Check cache first
  let user = await cache.get(`user:${userId}`);
  
  if (!user) {
    // Cache miss - fetch from database
    user = await database.getUser(userId);
    // Store in cache for future requests
    await cache.set(`user:${userId}`, user, 3600); // 1 hour TTL
  }
  
  return user;
}
```

2. Write-Through:
- Write to cache and database simultaneously
- Ensures cache consistency
- Higher write latency

3. Write-Behind (Write-Back):
- Write to cache immediately
- Asynchronously write to database
- Risk of data loss if cache fails

4. Refresh-Ahead:
- Proactively refresh cache before expiration
- Reduces cache miss latency
- More complex implementation

Cache Invalidation Strategies:
1. TTL (Time To Live): Automatic expiration
2. Manual Invalidation: Explicit cache clearing
3. Event-based Invalidation: Invalidate on data changes

Implementation in my projects:
- Domain HQ: Used Redis for analytics data caching
- BookMyService: Implemented multi-level caching for service search
- Video Calling App: Cached user presence and room information

## 3. Database Design & Data Storage

9-20. [Database design questions covering SQL vs NoSQL, ACID properties, normalization, etc.]

## 4. Microservices & Distributed Systems

21-30. [Microservices architecture, service communication, distributed transactions, etc.]

## 5. Security & Reliability

31-40. [Security considerations, authentication, data protection, fault tolerance, etc.]

## 6. Real-world System Design Examples

41-50. [Design popular systems like Twitter, WhatsApp, URL shortener, chat systems, etc.]

Key system design interview strategies:
- Start with requirements gathering and clarification
- Estimate scale and identify constraints
- Design high-level architecture first
- Deep dive into critical components
- Discuss trade-offs and alternatives
- Consider scalability, reliability, and performance
- Address security and monitoring concerns
- Be prepared to modify design based on new requirements
- Use diagrams and examples to explain concepts
- Show understanding of real-world constraints and challenges

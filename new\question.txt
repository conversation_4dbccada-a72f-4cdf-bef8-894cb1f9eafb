# 1) JavaScript — 10 questions

1. Explain `this` in JavaScript — how does its value change in different contexts?
2. What are closures? Give a real-world example where you used them.
3. How do `let`, `const`, and `var` differ under the hood (scope & hoisting)?
4. Describe the event loop and how async tasks are scheduled (microtask vs macrotask).
5. How do `==` and `===` differ and why prefer one over the other?
6. Write (or describe) how to remove duplicates from an array — mention time/space trade-offs.
7. How do `map`, `filter`, and `reduce` work — show a combined example.
8. What are Promises? How does `async/await` change error handling compared to `.then()`?
9. Explain prototype-based inheritance and how ES6 classes relate to prototypes.
10. What are memory leaks in JavaScript and how would you detect/fix them?

# 2) React — 10 questions

1. Explain the virtual DOM and how <PERSON>act decides what to re-render.
2. When would you use `useEffect` with an empty dependency array vs with dependencies?
3. How do you manage global state in a medium-sized app — Context vs Redux vs other?
4. What is the purpose of keys in lists, and what bugs occur if keys are poorly chosen?
5. Explain controlled vs uncontrolled components with form examples.
6. How do you prevent unnecessary re-renders? Mention `React.memo`, `useMemo`, `useCallback`.
7. Describe how you would implement code-splitting and lazy-loading in React.
8. What are error boundaries and when should you use them?
9. How does server-side rendering (Next.js) benefit SEO and performance?
10. Explain how you’d pass data from a deeply nested child component back to the top (patterns).

# 3) Node.js — 10 questions

1. How does Node.js handle concurrency while being single-threaded? Explain event loop & worker threads.
2. What are streams in Node.js and when would you use them instead of `readFile`?
3. Explain middleware in Express and how you’d write an authentication middleware.
4. How do you structure a scalable Express app (folders, routers, services)?
5. How do you securely store and use environment variables in production?
6. Explain `require` vs `import` — when would you use each?
7. How do you handle file uploads in Node.js (large files, memory usage)?
8. How would you debug a memory leak or high CPU usage in a Node process?
9. Explain clustering and when to use `pm2` or worker threads for scaling.
10. How do you implement input validation and sanitize user input in your APIs?

# 4) MongoDB — 10 questions

1. Why choose MongoDB for a project — strengths and trade-offs vs relational DBs?
2. How do you design a schema in MongoDB for a booking system (users, services, bookings)?
3. Explain indexing and how you would optimize a slow query.
4. How do you implement pagination efficiently with large collections?
5. What are aggregation pipelines — give an example use-case (grouping, lookup).
6. When would you embed documents vs reference them? Explain pros & cons.
7. How does replication work in MongoDB and what is automatic failover?
8. What is sharding and when should you consider it?
9. How do you handle migrations and schema evolution in MongoDB?
10. Describe how you would secure a MongoDB deployment (authentication, network rules).

# 5) Project-focused (Domain HQ, Video Call App, BookMyService) — 10 questions

1. Walk me through the architecture of BookMyService — from frontend to DB.
2. How did you implement role-based access (user, provider, admin) in BookMyService?
3. Explain the signaling flow in your WebRTC video calling app — how peers exchange SDP/ICE.
4. How did you handle reconnection or network drops in the video calling app?
5. Why did you choose Prisma for Domain HQ and how does it integrate with your backend?
6. How did you implement payments (Stripe) securely and what steps did you take for testing?
7. Explain one hard bug you faced in any project and how you resolved it step-by-step.
8. How do you deploy and maintain environment-specific configs (dev, staging, prod) for these projects?
9. How do you implement search & filtering for articles/books with performance in mind?
10. If you had to scale BookMyService to 10× users, what are the first three changes you’d make?

# 6) Behavioral & soft-skill questions — 10 questions

1. Tell me about a time you missed a deadline — what happened and what did you change?
2. Describe a conflict with a teammate and how you resolved it.
3. Give an example where feedback improved your work; what did you change?
4. How do you prioritize tasks when you have multiple urgent items?
5. Describe a situation where you took initiative at work.
6. How do you learn a new technology quickly before a deadline? Give a recent example.
7. How do you handle repetitive or boring tasks?
8. Tell me about a time you had to explain a technical concept to a non-technical person.
9. What is your biggest professional weakness and how are you improving it?
10. What are your short-term goals for the next 12 months if you join us?

# 7) OOP & CS fundamentals — 10 questions

1. Explain encapsulation, inheritance, and polymorphism with JavaScript examples.
2. How would you implement a stack and queue in JavaScript — complexity of operations?
3. Explain time complexity (Big O) of common algorithms (search, sort) — give examples.
4. What is normalization in databases and when might denormalization be better?
5. How does hashing work and where is it used in web apps?
6. Explain recursion and when recursion may be a poor choice.
7. Describe how you’d find cycles in a graph — high-level approach.
8. What is ACID in databases and why it matters?
9. Explain how sessions/cookies differ from JWT-based authentication.
10. How do you approach algorithmic problem-solving during coding interviews?

# 8) Deployment & Version Control — 10 questions

1. How do you deploy a Node.js + React app to a VPS using Nginx as reverse proxy?
2. What is PM2 and why use it in production? How do you configure auto-restart?
3. How do you set up SSL (HTTPS) on a VPS for your domain?
4. How do you perform zero-downtime deployments? Explain blue/green or rolling strategy.
5. How do you connect a hosted frontend to a backend with different domains (CORS & security)?
6. Explain a CI/CD pipeline you would build for a MERN app (tests, build, deploy).
7. How do you handle environment variables securely in CI (secrets)?
8. Explain `git rebase` vs `git merge` and when you’d use each in a team workflow.
9. How do you resolve a complex merge conflict? Describe the steps.
10. How do you roll back a bad deployment quickly and safely?

---

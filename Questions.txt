## **📌 Top 50 JavaScript Questions for MERN Stack Theory Round**

### **1. Basics & Data Types**

1. What are the different data types in JavaScript?
2. Difference between `var`, `let`, and `const`.
3. What is hoisting in JavaScript?
4. Explain the difference between `==` and `===`.
5. What is `NaN` in JavaScript and how to check for it?
6. How does JavaScript handle type conversion?
7. What is `typeof null` and why is it `"object"`?
8. What is the difference between `undefined` and `null`?
9. How are primitive types and reference types stored in memory?
10. What is a template literal in JavaScript?

---

### **2. Functions & Scope**

11. What are arrow functions and how are they different from regular functions?
12. What is the difference between function declaration and function expression?
13. What is lexical scope in JavaScript?
14. What is a closure and give an example.
15. What is the difference between synchronous and asynchronous functions?
16. How does `this` keyword work in JavaScript?
17. Difference between `.call()`, `.apply()`, and `.bind()`.
18. What are default parameters in functions?
19. What is the difference between pure and impure functions?
20. Explain higher-order functions in JavaScript.

---

### **3. Arrays & Objects**

21. Difference between `for...of` and `for...in` loops.
22. How do `map()`, `filter()`, and `reduce()` work?
23. Difference between `forEach()` and `map()`.
24. How do you clone an object in JavaScript?
25. How do you merge two arrays or two objects?
26. How to remove duplicates from an array in JavaScript?
27. Difference between `slice()` and `splice()`.
28. How to destructure arrays and objects in ES6?
29. What is the spread operator and rest parameter?
30. How do you check if a property exists in an object?

---

### **4. Asynchronous JavaScript**

31. What is the event loop in JavaScript?
32. Difference between microtasks and macrotasks.
33. What is a Promise and its states?
34. Difference between `async/await` and `.then()` syntax.
35. How does `setTimeout()` work internally?
36. What is `Promise.all()` and `Promise.race()`?
37. How do you handle errors in Promises?
38. What is the difference between callbacks and Promises?
39. How does JavaScript handle asynchronous code execution?
40. What is the difference between `fetch()` and `XMLHttpRequest`?

---

### **5. DOM, Events & Browser Concepts**

41. Difference between `document.getElementById()` and `querySelector()`.
42. What are event bubbling and event capturing?
43. How do you prevent default behavior of an event?
44. Difference between `localStorage`, `sessionStorage`, and cookies.
45. What is debouncing and throttling in JavaScript?
46. What are web workers in JavaScript?
47. What is the difference between `innerHTML` and `textContent`?
48. How do you dynamically create HTML elements with JavaScript?
49. What is CORS and how does JavaScript handle it?
50. How does JavaScript handle memory management and garbage collection?

------------------------------------------------------------------------------------------------------------------

## **📌 Top 50 React.js Theory Questions**

### **1. React Basics**

1. What is React and why is it used?
2. What are the main features of React?
3. What is the difference between React and other JavaScript frameworks like Angular or Vue?
4. What is the virtual DOM and how does it work?
5. What is JSX and why do we use it?
6. Can browsers read JSX directly? Why or why not?
7. What is the role of `ReactDOM.render()`?
8. What is the difference between functional and class components?
9. What are props in React?
10. What is the difference between props and state?

---

### **2. State & Lifecycle**

11. How do you manage state in a React component?
12. What are the rules for using React Hooks?
13. What is the difference between `useState` and `useReducer`?
14. Explain the lifecycle methods of class components.
15. How do you replicate lifecycle methods using hooks?
16. What is `useEffect` and when does it run?
17. How do you prevent unnecessary re-renders in React?
18. What is the difference between local state and global state?
19. What is the Context API in React?
20. How do you pass data from a child to a parent component?

---

### **3. Rendering & Performance**

21. What is conditional rendering in React?
22. What is the difference between controlled and uncontrolled components?
23. What is reconciliation in React?
24. Why do we use `key` in React lists?
25. What happens if you don’t use keys in React lists?
26. What is memoization in React and when should you use it?
27. What is `React.memo()` and how is it different from `useMemo()`?
28. How do you optimize a React app’s performance?
29. What is lazy loading in React?
30. How do you split code in React?

---

### **4. Forms & Events**

31. How do you handle forms in React?
32. Difference between controlled and uncontrolled form inputs.
33. How do you validate form inputs in React?
34. How do you handle multiple form inputs in React?
35. What are synthetic events in React?
36. How do you stop event bubbling in React?
37. What is the difference between `onChange` and `onInput`?
38. How do you use refs in React?
39. What is `forwardRef()` in React?
40. When would you use `useRef()` hook?

---

### **5. Advanced Concepts**

41. What is server-side rendering (SSR) and how does React support it?
42. What is hydration in React?
43. What is the difference between CSR, SSR, and SSG?
44. How do you handle side effects in React?
45. What is an error boundary in React?
46. What are React fragments and why are they used?
47. What is the difference between `React.Fragment` and `<>` syntax?
48. How do you use `useCallback()` hook?
49. What is the difference between `useMemo()` and `useEffect()`?
50. How do you manage environment variables in a React app?

---------------------------------------------------------------------------------------------------------------------

## **📌 Top 50 Node.js Theory Questions**

### **1. Node.js Basics**

1. What is Node.js and why is it used?
2. Is Node.js single-threaded? Explain.
3. What is the difference between Node.js and a browser JavaScript environment?
4. What is the V8 engine in Node.js?
5. What are the main features of Node.js?
6. What is the difference between asynchronous and synchronous programming in Node.js?
7. What is the role of the event loop in Node.js?
8. What is `npm` and why is it important?
9. Difference between `npm` and `npx`.
10. What is `package.json` and what is it used for?

---

### **2. Modules & Architecture**

11. What are Node.js modules?
12. Difference between CommonJS (`require`) and ES Modules (`import`).
13. What are core modules in Node.js?
14. How do you create a custom module in Node.js?
15. What is the difference between `exports` and `module.exports`?
16. What is middleware in Node.js?
17. How does the `require` function work internally?
18. What is the difference between relative and absolute paths in Node.js?
19. How do you resolve module paths in Node.js?
20. What is the difference between global objects in Node.js and browser?

---

### **3. File System & Streams**

21. How do you read and write files in Node.js?
22. Difference between synchronous and asynchronous file operations.
23. What is a stream in Node.js?
24. Different types of streams in Node.js.
25. What is the difference between `readFile` and `createReadStream`?
26. What is `pipe()` in Node.js streams?
27. How do you handle large file uploads in Node.js?
28. What are buffers in Node.js?
29. Difference between buffer and stream.
30. How do you delete or rename files in Node.js?

---

### **4. Events & Async Handling**

31. What is the EventEmitter class in Node.js?
32. How do you create and listen to custom events in Node.js?
33. What is the difference between callbacks, promises, and async/await in Node.js?
34. How do you handle errors in asynchronous code?
35. What is process.nextTick() in Node.js?
36. Difference between `setImmediate()` and `process.nextTick()`.
37. What are microtasks and macrotasks in Node.js?
38. How does Node.js handle concurrency despite being single-threaded?
39. What is clustering in Node.js?
40. What is worker\_threads module in Node.js?

---

### **5. Networking, Security & Deployment**

41. How do you create a basic HTTP server in Node.js?
42. What is the difference between HTTP and HTTPS in Node.js?
43. How do you implement JWT authentication in Node.js?
44. What is CORS in Node.js and how do you enable it?
45. How do you handle environment variables in Node.js?
46. What is rate limiting in APIs and how do you implement it in Node.js?
47. How do you secure sensitive data in Node.js applications?
48. What is the difference between process.env and config files?
49. How do you deploy a Node.js application on a VPS using Nginx?
50. What are some common Node.js best practices for performance and security?

-----------------------------------------------------------------------------------------------------------------------

## **📌 Top 50 MongoDB Theory Questions**

### **1. MongoDB Basics**

1. What is MongoDB and why is it used?
2. Is MongoDB SQL or NoSQL? Explain.
3. What are the main features of MongoDB?
4. Difference between MongoDB and relational databases.
5. What is a document in MongoDB?
6. What is a collection in MongoDB?
7. What is the `_id` field in MongoDB?
8. What is BSON in MongoDB?
9. Difference between JSON and BSON.
10. How does MongoDB store data internally?

---

### **2. CRUD Operations**

11. How do you insert a document into a collection?
12. Difference between `insertOne()` and `insertMany()`.
13. How do you read/find data in MongoDB?
14. Difference between `find()` and `findOne()`.
15. How do you update a document in MongoDB?
16. Difference between `updateOne()` and `updateMany()`.
17. What is the `$set` operator in MongoDB?
18. How do you delete documents in MongoDB?
19. Difference between `deleteOne()` and `deleteMany()`.
20. How do you sort query results in MongoDB?

---

### **3. Query & Operators**

21. What are MongoDB comparison operators?
22. What is the difference between `$gt`, `$gte`, `$lt`, `$lte`?
23. What is the `$in` operator in MongoDB?
24. What is the `$and` and `$or` operator?
25. How do you perform pattern matching in MongoDB?
26. What is the `$exists` operator?
27. How do you filter nested documents in MongoDB?
28. What is projection in MongoDB?
29. How do you return only specific fields from a document?
30. What is the difference between `.pretty()` and normal output?

---

### **4. Indexing & Performance**

31. What is an index in MongoDB?
32. How do you create an index?
33. Difference between single-field and compound indexes.
34. What is the `_id` index?
35. How do indexes improve query performance?
36. What is an aggregation pipeline in MongoDB?
37. Difference between `$match` and `$project` in aggregation.
38. What is `$group` in MongoDB aggregation?
39. How do you count documents in a collection?
40. Difference between `count()` and `countDocuments()`.

---

### **5. Advanced Concepts**

41. What is sharding in MongoDB?
42. What is replication in MongoDB?
43. Difference between primary and secondary replica sets.
44. What is the oplog in MongoDB replication?
45. How does MongoDB ensure high availability?
46. What is the difference between `find()` and `aggregate()`?
47. How do you implement text search in MongoDB?
48. What is the `$lookup` stage in aggregation?
49. How do you perform pagination in MongoDB?
50. What are MongoDB best practices for performance optimization?

----------------------------------------------------------------------------------------------------------------------------

## **📌 Top 50 Behavioral Interview Questions**

### **1. Personal & Self-Awareness**

1. Tell me about yourself.
2. What motivates you to work in software development?
3. What are your greatest strengths?
4. What is one weakness you are working to improve?
5. How do you stay updated with new technologies?
6. How do you handle criticism?
7. What do you enjoy most about coding?
8. What do you enjoy least about coding?
9. How do you prioritize tasks in your work?
10. How do you keep yourself organized?

---

### **2. Teamwork & Communication**

11. Tell me about a time you worked in a team to complete a project.
12. How do you handle conflicts with teammates?
13. How do you communicate progress to non-technical stakeholders?
14. Give an example of when you helped a teammate solve a problem.
15. How do you handle differences of opinion in a team?
16. Describe a situation when you had to explain a complex technical issue in simple terms.
17. How do you make sure you’re aligned with your team’s goals?
18. How do you handle working with a difficult colleague?
19. Have you ever taken the lead on a project? How did it go?
20. How do you handle situations where team members miss deadlines?

---

### **3. Problem-Solving & Decision-Making**

21. Tell me about a time you solved a complex technical problem.
22. Describe a project challenge you faced and how you overcame it.
23. How do you approach debugging a problem you’ve never seen before?
24. Tell me about a time you had to make a decision with limited information.
25. How do you handle situations when a project requirement is unclear?
26. Have you ever found a more efficient way to do something at work?
27. How do you handle urgent bug fixes while working on other tasks?
28. Tell me about a time you had to learn a new technology quickly.
29. How do you make sure your code is maintainable?
30. How do you balance speed vs. quality in coding?

---

### **4. Adaptability & Learning**

31. Tell me about a time you had to adapt to a big change in a project.
32. How do you handle situations when your original plan doesn’t work?
33. Have you ever missed a project deadline? What happened?
34. How do you handle stress during tight deadlines?
35. How do you react when you receive new requirements late in a project?
36. Have you ever worked on multiple projects at the same time? How did you manage?
37. How do you keep improving your skills outside of work?
38. Have you ever had to take on a task outside your comfort zone?
39. Tell me about a time you learned from a mistake.
40. How do you handle unfamiliar tasks or technologies?

---

### **5. Career Goals & Company Fit**

41. Why do you want to work here?
42. What do you know about our company?
43. How does this role fit into your career goals?
44. Where do you see yourself in 2–3 years?
45. Why should we hire you over other candidates?
46. What is your ideal work environment?
47. How do you handle repetitive tasks?
48. What do you expect from your manager?
49. What do you expect from your teammates?
50. Do you have any questions for us?

-----------------------------------------------------------------------------------------------------------------------

## **📌 Top 50 Resume-Based Questions (Tailored for Your CV)**

### **1. General Profile**

1. Walk me through your resume.
2. Why did you choose to become a MERN stack developer?
3. You have 9+ months of experience — what are your main takeaways from that?
4. How do you keep your technical skills up to date?
5. What was the biggest challenge you faced in your career so far?
6. What’s your strongest skill in the MERN stack?
7. Which do you prefer — frontend or backend, and why?
8. Why did you choose Bhagwan Mahavir University for your degree?
9. You’re in the top 3% of your batch — how did you achieve that?
10. Which coursework was most useful for your career?

---

### **2. Technical Skills**

11. Explain the MERN stack and how its components work together.
12. How does Next.js improve a React project?
13. When would you use RESTful APIs vs. GraphQL?
14. What’s the difference between WebSocket and WebRTC in real-time apps?
15. How do you secure an API in Node.js?
16. What’s your approach to debugging in VS Code?
17. How do you use Postman during development?
18. Why did you choose Tailwind CSS over Bootstrap in certain projects?
19. How do you handle file uploads with Cloudinary?
20. Explain how you deploy apps with VPS and Nginx.

---

### **3. Experience at AlgoScript Software**

21. Tell me about the most impactful feature you built at AlgoScript.
22. How did you optimize features to increase user engagement by 20%?
23. Explain the authentication flow you implemented with JWT and bcrypt.
24. How do you structure reusable React components?
25. How did you integrate MongoDB Atlas and Mongoose for filtering & pagination?
26. Describe how Agile sprints worked in your team.
27. What’s your process for participating in code reviews?
28. How do you handle client demo preparation?
29. Tell me about a time you missed a deadline at AlgoScript.
30. How do you ensure mobile-first responsive design?

---

### **4. Projects**

31. How does your **Domain HQ** dashboard work technically?
32. Why did you choose Prisma for Domain HQ?
33. How did you implement dynamic article rendering?
34. What kind of CRUD operations exist in your Domain HQ CMS?
35. How does your **Video Calling Web App** use WebRTC & Socket.IO?
36. How do you handle connection drops in your video calling app?
37. Why did you choose Render for deployment?
38. Explain the **BookMyService** platform architecture.
39. How does your OTP verification work in BookMyService?
40. How did you implement Stripe integration in BookMyService?

---

### **5. Problem-Solving & Impact**

41. Which project was the most challenging and why?
42. How do you ensure security in full-stack applications?
43. Tell me about a bug you fixed that took a lot of effort.
44. Have you ever had to refactor large parts of a project?
45. How do you handle real-time data in your apps?
46. How do you manage state between multiple components in React?
47. Which part of backend development do you find most difficult?
48. How do you test your APIs before deployment?
49. Which skill from your resume do you want to improve further?
50. If you could rebuild one of your projects, what would you do differently?

---------------------------------------------------------------------------------------------------------------------------

## **📌 Top 50 Project-Based Interview Questions (Tailored to Your Resume)**

### **Domain HQ – Blog Admin Dashboard**

1. What is the purpose of Domain HQ and who are its intended users?
2. Why did you choose **Next.js** for this project?
3. How did you implement dynamic article rendering based on assigned domains?
4. Can you explain the CMS features you built?
5. How did you integrate CRUD operations in the dashboard?
6. Why did you choose Prisma for database management?
7. How do you handle authentication in Domain HQ?
8. How did you ensure performance optimization in the dashboard?
9. What challenges did you face while implementing personalized content delivery?
10. How do you manage API calls and state in this project?

---

### **Video Calling Web App**

11. How does WebRTC work in your application?
12. Why did you choose Socket.IO alongside WebRTC?
13. How do you establish a peer-to-peer connection?
14. How does your app handle reconnection after a dropped call?
15. What measures did you take for secure communication?
16. Why did you choose Node.js and Express for the backend?
17. How do you handle signaling between peers?
18. How did you optimize performance for video streaming?
19. Why did you deploy on Render, and what were the limitations?
20. If you had more resources, how would you improve the app?

---

### **BookMyService – Service Booking Platform**

21. What is the main functionality of BookMyService?
22. Why did you choose React.js with Vite for the frontend?
23. How does role-based access work for different users?
24. How did you implement real-time bookings?
25. Explain the OTP verification process for service completion.
26. Why did you integrate Stripe, and how does it work?
27. How do you handle authentication and authorization?
28. What database schema design did you follow for this platform?
29. How did you manage state across the app using Context API?
30. How do you ensure data security in BookMyService?

---

### **AlgoScript Software Pvt. Ltd. Work**

31. Tell me about a feature you developed that improved user engagement by 20%.
32. How did you structure your RESTful APIs?
33. What authentication method did you use in your APIs?
34. How do you ensure your APIs are scalable?
35. How did you handle pagination and filtering in MongoDB?
36. How do you build reusable React components for multiple projects?
37. What’s your approach for making mobile-first designs?
38. How did you collaborate with UI/UX teams?
39. How do you handle deployment on a VPS with Nginx?
40. Tell me about an Agile sprint you contributed to and what your role was.

---

### **General Project Discussion**

41. Which project are you most proud of and why?
42. How do you decide which tech stack to use for a project?
43. How do you ensure that your projects are maintainable in the long run?
44. Have you faced performance issues in any project? How did you solve them?
45. How do you handle API errors in your projects?
46. How do you test your applications before deployment?
47. Have you ever integrated third-party APIs? Give an example.
48. How do you manage sensitive data like API keys in your projects?
49. How do you approach debugging a feature that doesn’t work as expected?
50. If you were to rebuild one of your projects from scratch, what would you change?

-----------------------------------------------------------------------------------------------------------
---

## **📌 50 OOP & CS Fundamentals Questions for MERN Stack Interviews**

### **1. OOP Concepts in JavaScript (10 Questions)**

1. What is Object-Oriented Programming?
2. Explain encapsulation with an example in JavaScript.
3. What is inheritance? How do you implement it in JavaScript?
4. What is polymorphism? Give an example.
5. Explain abstraction with an example.
6. Difference between class and object in JavaScript.
7. What is the difference between `prototype` and `class` in JS?
8. Explain method overriding vs method overloading in JavaScript.
9. What are getters and setters in JavaScript classes?
10. What is the difference between `static` and instance methods?

---

### **2. Data Structures Basics (10 Questions)**

11. What is an array, and how is it implemented in JavaScript?
12. Difference between array and linked list.
13. What is a stack? How do you implement it in JavaScript?
14. What is a queue? Give a real-world example.
15. What is a hash table or map?
16. How do you search for an element in an array?
17. Difference between linear search and binary search.
18. What is the time complexity of common array operations?
19. What is a tree data structure?
20. What is a graph, and where is it used?

---

### **3. Algorithms & Complexity (8 Questions)**

21. Explain Big O notation.
22. What is the time complexity of binary search?
23. Explain bubble sort and its complexity.
24. Explain quicksort and its complexity.
25. How do you check if a string is a palindrome in JavaScript?
26. What’s the difference between recursion and iteration?
27. Give an example of a recursive function in JS.
28. How do you find the largest number in an array?

---

### **4. Database Fundamentals (8 Questions)**

29. What is a database?
30. Difference between SQL and NoSQL databases.
31. What is normalization in databases?
32. What is denormalization, and when would you use it?
33. What is an index in a database?
34. Explain primary key vs foreign key.
35. What is ACID in database transactions?
36. What is a join in SQL, and types of joins?

---

### **5. Networking Basics (8 Questions)**

37. What is HTTP, and how does it work?
38. Difference between HTTP and HTTPS.
39. What is a REST API?
40. What is DNS, and how does it work?
41. Difference between GET and POST requests.
42. What is TCP/IP?
43. Explain the concept of a client-server architecture.
44. What is latency in networking?

---

### **6. Software Engineering & Misc (6 Questions)**

45. What is the Software Development Life Cycle (SDLC)?
46. Difference between waterfall and agile methodologies.
47. What is version control, and why is Git used?
48. What is debugging?
49. Difference between unit testing and integration testing.
50. How do you ensure code quality in a team project?

------------------------------------------------------------------------------------------------------------------
---

## **📌 50 Deployment & Version Control Interview Questions**

### **1. Deployment Basics (10 Questions)**

1. What does “deployment” mean in software development?
2. What is the difference between development, staging, and production environments?
3. What is the purpose of a build process before deployment?
4. What is server-side rendering (SSR) vs client-side rendering (CSR) in deployment?
5. What is the role of environment variables in deployment?
6. How do you store sensitive credentials securely for deployment?
7. What is the difference between a monolithic and microservices deployment?
8. What is downtime during deployment, and how can you avoid it?
9. What is the difference between blue-green deployment and rolling deployment?
10. How do you monitor a deployed application?

---

### **2. VPS & Hosting (10 Questions)**

11. What is a VPS (Virtual Private Server)?
12. How do you deploy a Node.js app on a VPS?
13. What is Nginx, and why is it used in deployment?
14. How do you configure Nginx as a reverse proxy?
15. What is the difference between Apache and Nginx?
16. How do you restart a Node.js app after a crash?
17. What is PM2, and why is it used in deployment?
18. How do you handle SSL certificates for HTTPS on a VPS?
19. How do you set up a domain name for a deployed app?
20. How do you configure firewall and security on a VPS?

---

### **3. Cloud & Managed Deployment (10 Questions)**

21. How do you deploy a Node.js/React app on Render/Heroku/Vercel?
22. What is the difference between Vercel and Netlify?
23. How do you deploy a MERN stack app on AWS EC2?
24. How do you connect a MongoDB Atlas database to a deployed backend?
25. How do you deploy a static site on GitHub Pages?
26. What is the difference between container-based and serverless deployment?
27. What is Docker, and why is it used in deployment?
28. What is Kubernetes, and when would you use it?
29. How do you handle environment-specific configurations in deployment?
30. What are the pros and cons of serverless deployment (e.g., AWS Lambda)?

---

### **4. CI/CD & Automation (10 Questions)**

31. What is CI/CD in software development?
32. How does GitHub Actions work for deployment automation?
33. What is the difference between continuous integration and continuous delivery?
34. How do you write a GitHub Actions workflow for a MERN stack app?
35. What is Jenkins, and how is it used in deployment pipelines?
36. How do you set up automated testing before deployment?
37. How do you roll back a failed deployment?
38. What is artifact management in CI/CD pipelines?
39. How do you handle database migrations in CI/CD?
40. How do you monitor and log errors after deployment automatically?

---

### **5. Version Control (Git & GitHub) (10 Questions)**

41. What is Git, and how is it different from GitHub?
42. How do you clone a Git repository?
43. What is the difference between `git pull` and `git fetch`?
44. What is a Git branch, and why is it used?
45. How do you merge branches in Git?
46. What is a merge conflict, and how do you resolve it?
47. What is the difference between `git merge` and `git rebase`?
48. How do you revert to a previous commit in Git?
49. What is the purpose of `.gitignore`?
50. How do you create and review a pull request in GitHub?

---

# JavaScript Interview Questions & Answers

## 1. Basics & Data Types

1. What are the different data types in JavaScript?

JavaScript has 8 data types:

Primitive Types:
- Number: Represents both integers and floating-point numbers (e.g., 42, 3.14)
- String: Text data enclosed in quotes (e.g., "Hello", 'World')
- Boolean: true or false values
- Undefined: Variable declared but not assigned a value
- Null: Intentional absence of value
- Symbol: Unique identifier (ES6+)
- BigInt: Large integers beyond Number.MAX_SAFE_INTEGER (ES2020)

Non-Primitive Type:
- Object: Complex data type including arrays, functions, objects, dates, etc.

```javascript
let num = 42;           // Number
let str = "Hello";      // String
let bool = true;        // Boolean
let undef;              // Undefined
let empty = null;       // Null
let sym = Symbol('id'); // Symbol
let big = 123n;         // BigInt
let obj = {};           // Object
```

2. Difference between `var`, `let`, and `const`.

| Feature | var | let | const |
|---------|-----|-----|-------|
| Scope | Function/Global | Block | Block |
| Hoisting | Yes (undefined) | Yes (TDZ) | Yes (TDZ) |
| Re-declaration | Allowed | Not allowed | Not allowed |
| Re-assignment | Allowed | Allowed | Not allowed |
| Temporal Dead Zone | No | Yes | Yes |

```javascript
// var - function scoped
function example() {
    if (true) {
        var x = 1;
    }
    console.log(x); // 1 (accessible outside block)
}

// let - block scoped
function example2() {
    if (true) {
        let y = 1;
    }
    console.log(y); // ReferenceError: y is not defined
}

// const - block scoped, immutable binding
const z = 1;
z = 2; // TypeError: Assignment to constant variable
```

3. What is hoisting in JavaScript?

Hoisting is JavaScript's behavior of moving variable and function declarations to the top of their scope during compilation. Only declarations are hoisted, not initializations.

```javascript
// Variable hoisting
console.log(x); // undefined (not ReferenceError)
var x = 5;

// Equivalent to:
var x;
console.log(x); // undefined
x = 5;

// Function hoisting
sayHello(); // "Hello!" (works before declaration)

function sayHello() {
    console.log("Hello!");
}

// let/const hoisting (Temporal Dead Zone)
console.log(y); // ReferenceError: Cannot access 'y' before initialization
let y = 10;
```

4. Explain the difference between `==` and `===`.

- `==` (Loose Equality): Compares values after type coercion
- `===` (Strict Equality): Compares values and types without coercion

```javascript
// Loose equality (==)
5 == "5"        // true (string "5" converted to number)
true == 1       // true (boolean converted to number)
null == undefined // true (special case)
0 == false      // true (both converted to 0)

// Strict equality (===)
5 === "5"       // false (different types)
true === 1      // false (different types)
null === undefined // false (different types)
0 === false     // false (different types)
```

5. What is `NaN` in JavaScript and how to check for it?

`NaN` (Not-a-Number) represents an invalid number result from mathematical operations.

```javascript
// NaN examples
let result1 = 0 / 0;        // NaN
let result2 = "hello" * 2;  // NaN
let result3 = Math.sqrt(-1); // NaN

// Checking for NaN
console.log(NaN === NaN);   // false (NaN is not equal to itself)
console.log(isNaN(NaN));    // true
console.log(Number.isNaN(NaN)); // true (preferred method)

// Difference between isNaN() and Number.isNaN()
isNaN("hello");        // true (converts to NaN first)
Number.isNaN("hello"); // false (doesn't convert, checks if actually NaN)
```

6. How does JavaScript handle type conversion?

JavaScript performs automatic type conversion (coercion) in certain contexts:

Implicit Coercion:
```javascript
// String concatenation
"5" + 3        // "53" (number to string)
"5" - 3        // 2 (string to number)
"5" * "2"      // 10 (both strings to numbers)

// Boolean context
if ("hello") { } // true (non-empty string is truthy)
if (0) { }       // false (0 is falsy)

// Comparison
"10" > 5       // true (string "10" converted to number)
```

Explicit Conversion:
```javascript
// To string
String(123)    // "123"
(123).toString() // "123"

// To number
Number("123")  // 123
parseInt("123px") // 123
parseFloat("12.34") // 12.34

// To boolean
Boolean(1)     // true
Boolean("")    // false
!!value        // double negation trick
```

7. What is `typeof null` and why is it `"object"`?

```javascript
typeof null // "object"
```

This is a well-known bug in JavaScript that exists for historical reasons. `null` should return "null", but it returns "object" due to how JavaScript was originally implemented. This behavior is maintained for backward compatibility.

Correct way to check for null:
```javascript
value === null              // true if null
value == null              // true if null or undefined
Object.prototype.toString.call(value) === '[object Null]' // true if null
```

8. What is the difference between `undefined` and `null`?

| undefined | null |
|-----------|------|
| Default value for uninitialized variables | Intentional absence of value |
| Function returns undefined if no return | Explicitly assigned by programmer |
| typeof undefined = "undefined" | typeof null = "object" |
| undefined == null (true) | null == undefined (true) |
| undefined === null (false) | null === undefined (false) |

```javascript
let a;              // undefined (declared but not assigned)
let b = null;       // null (explicitly assigned)

function test() {}  // returns undefined
let obj = { prop: null }; // null assigned intentionally

console.log(a == b);  // true (loose equality)
console.log(a === b); // false (strict equality)
```

9. How are primitive types and reference types stored in memory?

Primitive Types (Stack):
- Stored directly in memory location
- Passed by value
- Each variable has its own copy

Reference Types (Heap):
- Stored in heap memory
- Variable holds reference/pointer to memory location
- Passed by reference

```javascript
// Primitive types
let a = 5;
let b = a;    // b gets copy of a's value
a = 10;       // changing a doesn't affect b
console.log(b); // 5

// Reference types
let obj1 = { name: "John" };
let obj2 = obj1;  // obj2 gets reference to same object
obj1.name = "Jane"; // changing obj1 affects obj2
console.log(obj2.name); // "Jane"
```

10. What is a template literal in JavaScript?

Template literals are string literals that allow embedded expressions and multi-line strings using backticks (`).

```javascript
// Basic usage
const name = "John";
const age = 30;
const message = `Hello, my name is ${name} and I'm ${age} years old.`;

// Multi-line strings
const multiLine = `
    This is a
    multi-line
    string
`;

// Expression evaluation
const a = 5, b = 10;
console.log(`Sum: ${a + b}`); // "Sum: 15"

// Function calls
function greet(name) {
    return `Hello, ${name}!`;
}
console.log(`${greet("Alice")}`); // "Hello, Alice!"

// Tagged templates
function highlight(strings, ...values) {
    return strings.reduce((result, string, i) => {
        return result + string + (values[i] ? `<mark>${values[i]}</mark>` : '');
    }, '');
}

const user = "John";
const highlighted = highlight`User: ${user}`;
// "User: <mark>John</mark>"
```

## 2. Functions & Scope

11. What are arrow functions and how are they different from regular functions?

Arrow functions are a concise way to write functions introduced in ES6.

Syntax:
```javascript
// Regular function
function add(a, b) {
    return a + b;
}

// Arrow function
const add = (a, b) => a + b;

// Multiple lines
const multiply = (a, b) => {
    const result = a * b;
    return result;
};

// Single parameter (parentheses optional)
const square = x => x * x;

// No parameters
const greet = () => "Hello!";
```

Key Differences:

1. **`this` binding**: Arrow functions don't have their own `this`
```javascript
const obj = {
    name: "John",
    regularFunction: function() {
        console.log(this.name); // "John"
    },
    arrowFunction: () => {
        console.log(this.name); // undefined (inherits from outer scope)
    }
};
```

2. **No `arguments` object**:
```javascript
function regular() {
    console.log(arguments); // Arguments object available
}

const arrow = () => {
    console.log(arguments); // ReferenceError
};

// Use rest parameters instead
const arrowWithRest = (...args) => {
    console.log(args); // Array of arguments
};
```

3. **Cannot be used as constructors**:
```javascript
function RegularFunction() {
    this.name = "Regular";
}
const instance1 = new RegularFunction(); // Works

const ArrowFunction = () => {
    this.name = "Arrow";
};
const instance2 = new ArrowFunction(); // TypeError
```

4. **No hoisting**:
```javascript
console.log(regularFunc()); // Works due to hoisting

function regularFunc() {
    return "Regular";
}

console.log(arrowFunc()); // ReferenceError

const arrowFunc = () => "Arrow";

12. What is the difference between function declaration and function expression?

Function Declaration:
- Hoisted completely (can be called before declaration)
- Creates a named function
- Must have a function name

```javascript
// Can be called before declaration
sayHello(); // "Hello!"

function sayHello() {
    console.log("Hello!");
}
```

Function Expression:
- Not hoisted (cannot be called before assignment)
- Can be anonymous or named
- Assigned to a variable

```javascript
// Cannot be called before assignment
sayGoodbye(); // TypeError: sayGoodbye is not a function

var sayGoodbye = function() {
    console.log("Goodbye!");
};

// Named function expression
const namedFunc = function myFunc() {
    console.log("Named function expression");
};
```

13. What is lexical scope in JavaScript?

Lexical scope means that the scope of variables is determined by where they are declared in the code (lexically). Inner functions have access to variables in their outer scope.

```javascript
function outerFunction(x) {
    const outerVar = "I'm outer";

    function innerFunction(y) {
        const innerVar = "I'm inner";
        console.log(outerVar); // Can access outer variable
        console.log(x);        // Can access outer parameter
        console.log(y);        // Can access own parameter
    }

    innerFunction(20);
    // console.log(innerVar); // ReferenceError: innerVar is not defined
}

outerFunction(10);
```

**14. What is a closure and give an example.**

A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.

```javascript
// Basic closure example
function outerFunction(x) {
    return function innerFunction(y) {
        return x + y; // Inner function accesses outer variable 'x'
    };
}

const addFive = outerFunction(5);
console.log(addFive(3)); // 8 (5 + 3)

// Practical example: Counter
function createCounter() {
    let count = 0;

    return {
        increment: () => ++count,
        decrement: () => --count,
        getCount: () => count
    };
}

const counter = createCounter();
console.log(counter.increment()); // 1
console.log(counter.increment()); // 2
console.log(counter.getCount());  // 2

// Module pattern using closure
const calculator = (function() {
    let result = 0;

    return {
        add: (x) => result += x,
        subtract: (x) => result -= x,
        getResult: () => result,
        reset: () => result = 0
    };
})();
```

**15. What is the difference between synchronous and asynchronous functions?**

**Synchronous Functions:**
- Execute line by line
- Block execution until completion
- Return values immediately

```javascript
function syncFunction() {
    console.log("Start");
    for (let i = 0; i < 1000000000; i++) {
        // Blocking operation
    }
    console.log("End");
    return "Sync result";
}

const result = syncFunction(); // Blocks until completion
console.log(result);
```

**Asynchronous Functions:**
- Don't block execution
- Use callbacks, promises, or async/await
- Return immediately (often a promise)

```javascript
// Callback-based async
function asyncCallback(callback) {
    setTimeout(() => {
        callback("Async result");
    }, 1000);
}

// Promise-based async
function asyncPromise() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve("Promise result");
        }, 1000);
    });
}

// Async/await
async function asyncAwait() {
    const result = await asyncPromise();
    return result;
}
```

**16. How does `this` keyword work in JavaScript?**

The value of `this` depends on how a function is called:

**1. Global Context:**
```javascript
console.log(this); // Window object (browser) or global object (Node.js)
```

**2. Function Context:**
```javascript
function regularFunction() {
    console.log(this); // Window (non-strict) or undefined (strict mode)
}
```

**3. Object Method:**
```javascript
const obj = {
    name: "John",
    greet: function() {
        console.log(this.name); // "John" (this refers to obj)
    }
};
obj.greet();
```

**4. Constructor Function:**
```javascript
function Person(name) {
    this.name = name; // this refers to new instance
}
const person = new Person("Alice");
```

**5. Arrow Functions:**
```javascript
const obj = {
    name: "John",
    greet: () => {
        console.log(this.name); // undefined (inherits from outer scope)
    }
};
```

**6. Event Handlers:**
```javascript
button.addEventListener('click', function() {
    console.log(this); // Refers to the button element
});
```

**17. Difference between `.call()`, `.apply()`, and `.bind()`.**

These methods allow you to explicitly set the value of `this`:

**`.call()`** - Calls function with specified `this` and individual arguments:
```javascript
function greet(greeting, punctuation) {
    console.log(`${greeting}, ${this.name}${punctuation}`);
}

const person = { name: "John" };
greet.call(person, "Hello", "!"); // "Hello, John!"
```

**`.apply()`** - Same as call but takes arguments as an array:
```javascript
greet.apply(person, ["Hi", "?"]); // "Hi, John?"
```

**`.bind()`** - Returns a new function with bound `this` (doesn't call immediately):
```javascript
const boundGreet = greet.bind(person);
boundGreet("Hey", "."); // "Hey, John."

// Partial application
const sayHello = greet.bind(person, "Hello");
sayHello("!!!"); // "Hello, John!!!"
```

**18. What are default parameters in functions?**

Default parameters allow you to specify default values for function parameters:

```javascript
// ES6 default parameters
function greet(name = "Guest", greeting = "Hello") {
    console.log(`${greeting}, ${name}!`);
}

greet();                    // "Hello, Guest!"
greet("John");              // "Hello, John!"
greet("Alice", "Hi");       // "Hi, Alice!"

// Default parameters with expressions
function createUser(name, id = Date.now()) {
    return { name, id };
}

// Default parameters can reference other parameters
function calculateArea(width, height = width) {
    return width * height;
}

calculateArea(5);    // 25 (square)
calculateArea(5, 3); // 15 (rectangle)

// Default parameters with destructuring
function processUser({ name = "Anonymous", age = 0 } = {}) {
    console.log(`Name: ${name}, Age: ${age}`);
}

processUser();                        // "Name: Anonymous, Age: 0"
processUser({ name: "John" });        // "Name: John, Age: 0"
processUser({ name: "Alice", age: 25 }); // "Name: Alice, Age: 25"
```

**19. What is the difference between pure and impure functions?**

**Pure Functions:**
- Always return the same output for the same input
- No side effects (don't modify external state)
- Don't depend on external state

```javascript
// Pure function examples
function add(a, b) {
    return a + b; // Always returns same result for same inputs
}

function multiply(arr, factor) {
    return arr.map(x => x * factor); // Doesn't modify original array
}

function calculateTax(amount, rate) {
    return amount * rate; // No side effects
}
```

**Impure Functions:**
- May return different outputs for same inputs
- Have side effects (modify external state)
- Depend on external state

```javascript
// Impure function examples
let counter = 0;
function incrementCounter() {
    counter++; // Modifies external state
    return counter;
}

function addRandomNumber(x) {
    return x + Math.random(); // Different output for same input
}

function logAndReturn(value) {
    console.log(value); // Side effect (logging)
    return value;
}

const users = [];
function addUser(user) {
    users.push(user); // Modifies external array
    return users.length;
}
```

**20. Explain higher-order functions in JavaScript.**

Higher-order functions are functions that either:
1. Take other functions as arguments, or
2. Return functions as results

```javascript
// Function that takes another function as argument
function applyOperation(arr, operation) {
    return arr.map(operation);
}

const numbers = [1, 2, 3, 4, 5];
const doubled = applyOperation(numbers, x => x * 2); // [2, 4, 6, 8, 10]

// Function that returns another function
function createMultiplier(factor) {
    return function(number) {
        return number * factor;
    };
}

const double = createMultiplier(2);
const triple = createMultiplier(3);

console.log(double(5)); // 10
console.log(triple(4)); // 12

// Built-in higher-order functions
const numbers2 = [1, 2, 3, 4, 5];

// map - transforms each element
const squared = numbers2.map(x => x * x); // [1, 4, 9, 16, 25]

// filter - selects elements based on condition
const evens = numbers2.filter(x => x % 2 === 0); // [2, 4]

// reduce - accumulates values
const sum = numbers2.reduce((acc, x) => acc + x, 0); // 15

// forEach - executes function for each element
numbers2.forEach(x => console.log(x));

// Chaining higher-order functions
const result = numbers2
    .filter(x => x > 2)    // [3, 4, 5]
    .map(x => x * 2)       // [6, 8, 10]
    .reduce((acc, x) => acc + x, 0); // 24
```

## 3. Arrays & Objects

21. Difference between `for...of` and `for...in` loops.

`for...in` iterates over enumerable properties (keys) of an object.
`for...of` iterates over iterable values (elements) of an array or other iterables.

```javascript
const arr = ['a', 'b', 'c'];
const obj = { name: 'John', age: 30 };

// for...in - iterates over keys/indices
for (let key in arr) {
    console.log(key);     // "0", "1", "2" (indices)
    console.log(arr[key]); // "a", "b", "c"
}

for (let key in obj) {
    console.log(key);     // "name", "age" (property names)
    console.log(obj[key]); // "John", 30
}

// for...of - iterates over values
for (let value of arr) {
    console.log(value);   // "a", "b", "c" (values)
}

// for...of doesn't work directly with objects
// for (let value of obj) { } // TypeError

// But works with Object.values(), Object.keys(), Object.entries()
for (let value of Object.values(obj)) {
    console.log(value);   // "John", 30
}
```

22. How do `map()`, `filter()`, and `reduce()` work?

map(): Creates a new array by transforming each element
```javascript
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(x => x * 2); // [2, 4, 6, 8, 10]
const objects = numbers.map(x => ({ value: x })); // [{value: 1}, {value: 2}, ...]
```

filter(): Creates a new array with elements that pass a test
```javascript
const numbers = [1, 2, 3, 4, 5];
const evens = numbers.filter(x => x % 2 === 0); // [2, 4]
const greaterThanThree = numbers.filter(x => x > 3); // [4, 5]
```

reduce(): Reduces array to a single value by accumulating
```javascript
const numbers = [1, 2, 3, 4, 5];
const sum = numbers.reduce((acc, x) => acc + x, 0); // 15
const max = numbers.reduce((acc, x) => Math.max(acc, x)); // 5
const product = numbers.reduce((acc, x) => acc * x, 1); // 120
```

23. Difference between `forEach()` and `map()`.

forEach(): Executes a function for each element, returns undefined
map(): Creates and returns a new array with transformed elements

```javascript
const numbers = [1, 2, 3];

// forEach - no return value, used for side effects
numbers.forEach(x => console.log(x * 2)); // Prints: 2, 4, 6
const result1 = numbers.forEach(x => x * 2); // undefined

// map - returns new array
const result2 = numbers.map(x => x * 2); // [2, 4, 6]
console.log(numbers); // [1, 2, 3] (original unchanged)
```

24. How do you clone an object in JavaScript?

Shallow Clone:
```javascript
const original = { a: 1, b: { c: 2 } };

// Object.assign()
const clone1 = Object.assign({}, original);

// Spread operator
const clone2 = { ...original };

// Object.create()
const clone3 = Object.create(Object.getPrototypeOf(original), Object.getOwnPropertyDescriptors(original));
```

Deep Clone:
```javascript
// JSON methods (limited - doesn't handle functions, dates, etc.)
const deepClone1 = JSON.parse(JSON.stringify(original));

// Recursive function
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// Using Lodash
const _ = require('lodash');
const deepClone2 = _.cloneDeep(original);
```

25. How do you merge two arrays or two objects?

Merging Arrays:
```javascript
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];

// Spread operator
const merged1 = [...arr1, ...arr2]; // [1, 2, 3, 4, 5, 6]

// concat()
const merged2 = arr1.concat(arr2); // [1, 2, 3, 4, 5, 6]

// push() with spread
arr1.push(...arr2); // Modifies arr1: [1, 2, 3, 4, 5, 6]
```

Merging Objects:
```javascript
const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const obj3 = { b: 5, e: 6 }; // Note: 'b' conflicts with obj1

// Spread operator (later properties override earlier ones)
const merged1 = { ...obj1, ...obj2, ...obj3 }; // { a: 1, b: 5, c: 3, d: 4, e: 6 }

// Object.assign()
const merged2 = Object.assign({}, obj1, obj2, obj3); // Same result

// Object.assign() modifying first object
Object.assign(obj1, obj2); // Modifies obj1
```

26. How to remove duplicates from an array in JavaScript?

```javascript
const arr = [1, 2, 2, 3, 4, 4, 5];

// Using Set
const unique1 = [...new Set(arr)]; // [1, 2, 3, 4, 5]

// Using filter with indexOf
const unique2 = arr.filter((item, index) => arr.indexOf(item) === index);

// Using reduce
const unique3 = arr.reduce((acc, current) => {
    if (!acc.includes(current)) {
        acc.push(current);
    }
    return acc;
}, []);

// For array of objects
const objArr = [
    { id: 1, name: 'John' },
    { id: 2, name: 'Jane' },
    { id: 1, name: 'John' }
];

const uniqueObjects = objArr.filter((item, index, self) =>
    index === self.findIndex(obj => obj.id === item.id)
);
```

27. Difference between `slice()` and `splice()`.

slice(): Returns a shallow copy of a portion of an array (non-mutating)
splice(): Changes the contents of an array by removing/adding elements (mutating)

```javascript
const arr = [1, 2, 3, 4, 5];

// slice(start, end) - doesn't modify original
const sliced = arr.slice(1, 4); // [2, 3, 4]
console.log(arr); // [1, 2, 3, 4, 5] (unchanged)

// splice(start, deleteCount, ...itemsToAdd) - modifies original
const spliced = arr.splice(1, 2, 'a', 'b'); // Returns [2, 3]
console.log(arr); // [1, 'a', 'b', 4, 5] (modified)

// Common splice uses
const fruits = ['apple', 'banana', 'orange', 'grape'];
fruits.splice(1, 1); // Remove 1 element at index 1: ['apple', 'orange', 'grape']
fruits.splice(1, 0, 'kiwi'); // Insert 'kiwi' at index 1: ['apple', 'kiwi', 'orange', 'grape']
fruits.splice(2, 1, 'mango'); // Replace element at index 2: ['apple', 'kiwi', 'mango', 'grape']
```

28. How to destructure arrays and objects in ES6?

Array Destructuring:
```javascript
const arr = [1, 2, 3, 4, 5];

// Basic destructuring
const [first, second] = arr; // first = 1, second = 2

// Skip elements
const [a, , c] = arr; // a = 1, c = 3

// Rest operator
const [head, ...tail] = arr; // head = 1, tail = [2, 3, 4, 5]

// Default values
const [x, y, z = 0] = [1, 2]; // x = 1, y = 2, z = 0

// Swapping variables
let p = 1, q = 2;
[p, q] = [q, p]; // p = 2, q = 1
```

Object Destructuring:
```javascript
const obj = { name: 'John', age: 30, city: 'New York' };

// Basic destructuring
const { name, age } = obj; // name = 'John', age = 30

// Rename variables
const { name: fullName, age: years } = obj; // fullName = 'John', years = 30

// Default values
const { name, country = 'USA' } = obj; // country = 'USA' (default)

// Nested destructuring
const user = {
    id: 1,
    profile: {
        name: 'Alice',
        email: '<EMAIL>'
    }
};
const { profile: { name: userName, email } } = user; // userName = 'Alice', email = '<EMAIL>'

// Function parameters
function greet({ name, age = 0 }) {
    console.log(`Hello ${name}, you are ${age} years old`);
}
greet({ name: 'Bob', age: 25 });
```

29. What is the spread operator and rest parameter?

Spread Operator (...): Expands iterables into individual elements
```javascript
// Arrays
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2]; // [1, 2, 3, 4, 5, 6]

// Objects
const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const merged = { ...obj1, ...obj2 }; // { a: 1, b: 2, c: 3, d: 4 }

// Function calls
function sum(a, b, c) {
    return a + b + c;
}
const numbers = [1, 2, 3];
console.log(sum(...numbers)); // 6

// Copying arrays/objects
const arrCopy = [...arr1]; // Shallow copy
const objCopy = { ...obj1 }; // Shallow copy
```

Rest Parameter (...): Collects multiple elements into an array
```javascript
// Function parameters
function sum(...numbers) {
    return numbers.reduce((acc, num) => acc + num, 0);
}
console.log(sum(1, 2, 3, 4)); // 10

// With other parameters
function greet(greeting, ...names) {
    return `${greeting} ${names.join(', ')}!`;
}
console.log(greet('Hello', 'John', 'Jane', 'Bob')); // "Hello John, Jane, Bob!"

// Array destructuring
const [first, ...rest] = [1, 2, 3, 4, 5]; // first = 1, rest = [2, 3, 4, 5]

// Object destructuring
const { name, ...otherProps } = { name: 'John', age: 30, city: 'NYC' };
// name = 'John', otherProps = { age: 30, city: 'NYC' }
```

30. How do you check if a property exists in an object?

```javascript
const obj = { name: 'John', age: 30, city: null };

// in operator
console.log('name' in obj); // true
console.log('salary' in obj); // false

// hasOwnProperty() - only own properties, not inherited
console.log(obj.hasOwnProperty('name')); // true
console.log(obj.hasOwnProperty('toString')); // false (inherited)

// Object.hasOwn() - ES2022, preferred over hasOwnProperty
console.log(Object.hasOwn(obj, 'name')); // true

// undefined check (careful with null/undefined values)
console.log(obj.name !== undefined); // true
console.log(obj.city !== undefined); // false (city is null)

// Object.keys()
console.log(Object.keys(obj).includes('name')); // true

// Reflect.has()
console.log(Reflect.has(obj, 'name')); // true
```

## 4. Asynchronous JavaScript

31. What is the event loop in JavaScript?

The event loop is the mechanism that allows JavaScript to perform non-blocking operations despite being single-threaded. It continuously monitors the call stack and task queues.

Components:
- Call Stack: Where function calls are executed
- Web APIs: Browser-provided APIs (setTimeout, DOM events, HTTP requests)
- Task Queue (Macrotask Queue): Callbacks from setTimeout, setInterval, I/O operations
- Microtask Queue: Promises, queueMicrotask, MutationObserver
- Event Loop: Moves tasks from queues to call stack when stack is empty

```javascript
console.log('1'); // Synchronous

setTimeout(() => console.log('2'), 0); // Macrotask

Promise.resolve().then(() => console.log('3')); // Microtask

console.log('4'); // Synchronous

// Output: 1, 4, 3, 2
// Microtasks have higher priority than macrotasks
```

32. Difference between microtasks and macrotasks.

Microtasks (higher priority):
- Promise callbacks (.then, .catch, .finally)
- queueMicrotask()
- MutationObserver
- Process.nextTick (Node.js)

Macrotasks (lower priority):
- setTimeout, setInterval
- setImmediate (Node.js)
- I/O operations
- UI rendering

```javascript
setTimeout(() => console.log('macrotask 1'), 0);

Promise.resolve().then(() => {
    console.log('microtask 1');
    return Promise.resolve();
}).then(() => console.log('microtask 2'));

setTimeout(() => console.log('macrotask 2'), 0);

Promise.resolve().then(() => console.log('microtask 3'));

// Output: microtask 1, microtask 2, microtask 3, macrotask 1, macrotask 2
```

33. What is a Promise and its states?

A Promise is an object representing the eventual completion or failure of an asynchronous operation.

States:
- Pending: Initial state, neither fulfilled nor rejected
- Fulfilled (Resolved): Operation completed successfully
- Rejected: Operation failed

```javascript
// Creating a Promise
const promise = new Promise((resolve, reject) => {
    const success = Math.random() > 0.5;

    setTimeout(() => {
        if (success) {
            resolve('Operation successful!');
        } else {
            reject(new Error('Operation failed!'));
        }
    }, 1000);
});

// Consuming a Promise
promise
    .then(result => console.log(result))
    .catch(error => console.error(error))
    .finally(() => console.log('Operation completed'));

// Promise states
console.log(promise); // Promise { <pending> }

// Promise methods
Promise.resolve('success'); // Immediately resolved
Promise.reject('error');    // Immediately rejected
```

34. Difference between `async/await` and `.then()` syntax.

.then() syntax (Promise chaining):
```javascript
function fetchUserData(id) {
    return fetch(`/api/users/${id}`)
        .then(response => response.json())
        .then(user => {
            console.log('User:', user);
            return fetch(`/api/posts/${user.id}`);
        })
        .then(response => response.json())
        .then(posts => {
            console.log('Posts:', posts);
            return posts;
        })
        .catch(error => {
            console.error('Error:', error);
            throw error;
        });
}
```

async/await syntax (more readable):
```javascript
async function fetchUserData(id) {
    try {
        const userResponse = await fetch(`/api/users/${id}`);
        const user = await userResponse.json();
        console.log('User:', user);

        const postsResponse = await fetch(`/api/posts/${user.id}`);
        const posts = await postsResponse.json();
        console.log('Posts:', posts);

        return posts;
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}
```

Key differences:
- async/await is more readable and easier to debug
- Error handling is simpler with try/catch
- .then() allows for more functional programming style
- async/await makes sequential operations clearer

35. How does `setTimeout()` work internally?

setTimeout() schedules a function to run after a specified delay, but the actual execution depends on the event loop.

```javascript
console.log('Start');

setTimeout(() => {
    console.log('Timeout 1');
}, 0);

setTimeout(() => {
    console.log('Timeout 2');
}, 0);

console.log('End');

// Output: Start, End, Timeout 1, Timeout 2
```

Internal process:
1. setTimeout() registers the callback with the Web API
2. After the delay, the callback is moved to the macrotask queue
3. Event loop checks if call stack is empty
4. If empty, moves callback from queue to call stack for execution

Important notes:
- Minimum delay is usually 4ms in browsers
- Delay is not guaranteed - it's the minimum time before execution
- Heavy synchronous operations can delay setTimeout execution

```javascript
// Demonstrating delay is not guaranteed
const start = Date.now();

setTimeout(() => {
    console.log(`Actual delay: ${Date.now() - start}ms`);
}, 100);

// Heavy synchronous operation
for (let i = 0; i < 1000000000; i++) {
    // This will delay the setTimeout callback
}
```

36. What is `Promise.all()` and `Promise.race()`?

Promise.all(): Waits for all promises to resolve, fails if any promise rejects
```javascript
const promise1 = Promise.resolve(1);
const promise2 = Promise.resolve(2);
const promise3 = Promise.resolve(3);

Promise.all([promise1, promise2, promise3])
    .then(values => console.log(values)) // [1, 2, 3]
    .catch(error => console.error(error));

// If any promise rejects
const failingPromise = Promise.reject('Error!');
Promise.all([promise1, failingPromise, promise3])
    .then(values => console.log(values))
    .catch(error => console.error(error)); // "Error!"

// Practical example
async function fetchMultipleUsers() {
    try {
        const [user1, user2, user3] = await Promise.all([
            fetch('/api/users/1').then(r => r.json()),
            fetch('/api/users/2').then(r => r.json()),
            fetch('/api/users/3').then(r => r.json())
        ]);
        return [user1, user2, user3];
    } catch (error) {
        console.error('One or more requests failed:', error);
    }
}
```

Promise.race(): Returns the first promise that settles (resolves or rejects)
```javascript
const slow = new Promise(resolve => setTimeout(() => resolve('slow'), 2000));
const fast = new Promise(resolve => setTimeout(() => resolve('fast'), 1000));

Promise.race([slow, fast])
    .then(value => console.log(value)) // "fast"
    .catch(error => console.error(error));

// Timeout pattern
function withTimeout(promise, ms) {
    const timeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), ms)
    );
    return Promise.race([promise, timeout]);
}

// Usage
withTimeout(fetch('/api/data'), 5000)
    .then(response => response.json())
    .catch(error => console.error(error)); // Will timeout after 5 seconds
```

37. How do you handle errors in Promises?

```javascript
// Using .catch()
promise
    .then(result => console.log(result))
    .catch(error => console.error('Error:', error));

// Using .then() with two arguments
promise.then(
    result => console.log(result),
    error => console.error('Error:', error)
);

// Using async/await with try/catch
async function handleAsync() {
    try {
        const result = await promise;
        console.log(result);
    } catch (error) {
        console.error('Error:', error);
    }
}

// Chaining with error handling
fetch('/api/data')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => console.log(data))
    .catch(error => console.error('Fetch error:', error));

// Global error handling
window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled promise rejection:', event.reason);
    event.preventDefault(); // Prevent default browser behavior
});
```

38. What is the difference between callbacks and Promises?

Callbacks:
```javascript
// Callback hell
function fetchUser(id, callback) {
    setTimeout(() => {
        if (id > 0) {
            callback(null, { id, name: 'John' });
        } else {
            callback(new Error('Invalid ID'), null);
        }
    }, 1000);
}

fetchUser(1, (error, user) => {
    if (error) {
        console.error(error);
        return;
    }

    fetchUserPosts(user.id, (error, posts) => {
        if (error) {
            console.error(error);
            return;
        }

        fetchPostComments(posts[0].id, (error, comments) => {
            if (error) {
                console.error(error);
                return;
            }
            console.log(comments);
        });
    });
});
```

Promises:
```javascript
function fetchUser(id) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            if (id > 0) {
                resolve({ id, name: 'John' });
            } else {
                reject(new Error('Invalid ID'));
            }
        }, 1000);
    });
}

// Promise chaining
fetchUser(1)
    .then(user => fetchUserPosts(user.id))
    .then(posts => fetchPostComments(posts[0].id))
    .then(comments => console.log(comments))
    .catch(error => console.error(error));

// async/await
async function getUserData() {
    try {
        const user = await fetchUser(1);
        const posts = await fetchUserPosts(user.id);
        const comments = await fetchPostComments(posts[0].id);
        console.log(comments);
    } catch (error) {
        console.error(error);
    }
}
```

Key differences:
- Promises avoid callback hell
- Better error handling with .catch()
- Composable and chainable
- Support for Promise.all(), Promise.race()
- async/await syntax for cleaner code

39. How does JavaScript handle asynchronous code execution?

JavaScript uses an event-driven, non-blocking I/O model:

1. Single-threaded execution with call stack
2. Web APIs handle asynchronous operations
3. Event loop manages task queues
4. Callbacks, Promises, and async/await for handling async results

```javascript
// Execution order demonstration
console.log('1 - Synchronous');

setTimeout(() => console.log('2 - Macrotask'), 0);

Promise.resolve().then(() => console.log('3 - Microtask'));

queueMicrotask(() => console.log('4 - Microtask'));

console.log('5 - Synchronous');

// Output: 1, 5, 3, 4, 2
```

40. What is the difference between `fetch()` and `XMLHttpRequest`?

XMLHttpRequest (older approach):
```javascript
const xhr = new XMLHttpRequest();
xhr.open('GET', '/api/data');
xhr.onreadystatechange = function() {
    if (xhr.readyState === 4 && xhr.status === 200) {
        const data = JSON.parse(xhr.responseText);
        console.log(data);
    }
};
xhr.onerror = function() {
    console.error('Request failed');
};
xhr.send();
```

fetch() (modern approach):
```javascript
fetch('/api/data')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => console.log(data))
    .catch(error => console.error('Fetch failed:', error));

// With async/await
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log(data);
    } catch (error) {
        console.error('Fetch failed:', error);
    }
}
```

Key differences:
- fetch() returns Promises, XMLHttpRequest uses callbacks
- fetch() has cleaner, more modern syntax
- fetch() doesn't reject on HTTP error status (4xx, 5xx)
- XMLHttpRequest has better browser support (older browsers)
- fetch() supports streaming and has better request/response handling

## 5. DOM, Events & Browser Concepts

41. Difference between `document.getElementById()` and `querySelector()`.

document.getElementById(): Returns element with specific ID (faster, more specific)
```javascript
const element = document.getElementById('myId'); // Returns single element or null
```

querySelector(): Returns first element matching CSS selector (more flexible)
```javascript
const element1 = document.querySelector('#myId'); // By ID
const element2 = document.querySelector('.myClass'); // By class
const element3 = document.querySelector('div[data-id="123"]'); // By attribute
const element4 = document.querySelector('div > p:first-child'); // Complex selector
```

querySelectorAll(): Returns all matching elements as NodeList
```javascript
const elements = document.querySelectorAll('.myClass'); // NodeList
elements.forEach(el => console.log(el));
```

Performance: getElementById() is faster for ID-based selection, querySelector() is more versatile.

42. What are event bubbling and event capturing?

Event Bubbling: Event starts from target element and bubbles up to parent elements
Event Capturing: Event starts from root and captures down to target element

```javascript
// HTML: <div id="outer"><div id="inner"><button id="button">Click</button></div></div>

const outer = document.getElementById('outer');
const inner = document.getElementById('inner');
const button = document.getElementById('button');

// Event bubbling (default)
outer.addEventListener('click', () => console.log('Outer clicked'));
inner.addEventListener('click', () => console.log('Inner clicked'));
button.addEventListener('click', () => console.log('Button clicked'));

// Clicking button outputs: Button clicked, Inner clicked, Outer clicked

// Event capturing (useCapture = true)
outer.addEventListener('click', () => console.log('Outer captured'), true);
inner.addEventListener('click', () => console.log('Inner captured'), true);
button.addEventListener('click', () => console.log('Button captured'), true);

// With capturing: Outer captured, Inner captured, Button captured

// Stop propagation
button.addEventListener('click', (event) => {
    console.log('Button clicked');
    event.stopPropagation(); // Stops bubbling/capturing
});
```

43. How do you prevent default behavior of an event?

```javascript
// Prevent form submission
document.querySelector('form').addEventListener('submit', (event) => {
    event.preventDefault();
    console.log('Form submission prevented');
    // Handle form data manually
});

// Prevent link navigation
document.querySelector('a').addEventListener('click', (event) => {
    event.preventDefault();
    console.log('Link click prevented');
    // Custom navigation logic
});

// Prevent context menu
document.addEventListener('contextmenu', (event) => {
    event.preventDefault();
    console.log('Right-click menu prevented');
});

// Prevent key press
document.addEventListener('keydown', (event) => {
    if (event.key === 'F12') {
        event.preventDefault();
        console.log('F12 key prevented');
    }
});

// Check if default was prevented
document.addEventListener('click', (event) => {
    if (event.defaultPrevented) {
        console.log('Default behavior was already prevented');
    }
});
```

44. Difference between `localStorage`, `sessionStorage`, and cookies.

localStorage: Persistent storage until manually cleared
```javascript
// Set data
localStorage.setItem('username', 'john');
localStorage.setItem('preferences', JSON.stringify({ theme: 'dark' }));

// Get data
const username = localStorage.getItem('username');
const preferences = JSON.parse(localStorage.getItem('preferences'));

// Remove data
localStorage.removeItem('username');
localStorage.clear(); // Remove all

// Storage event (fires when localStorage changes in other tabs)
window.addEventListener('storage', (event) => {
    console.log('Storage changed:', event.key, event.newValue);
});
```

sessionStorage: Persists only for browser session
```javascript
// Same API as localStorage
sessionStorage.setItem('tempData', 'value');
const tempData = sessionStorage.getItem('tempData');
sessionStorage.removeItem('tempData');
sessionStorage.clear();
```

Cookies: Sent with HTTP requests, can be set to expire
```javascript
// Set cookie
document.cookie = "username=john; expires=Thu, 18 Dec 2024 12:00:00 UTC; path=/";

// Get all cookies
console.log(document.cookie); // "username=john; theme=dark"

// Helper functions for cookies
function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

function deleteCookie(name) {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}
```

Comparison:
| Feature | localStorage | sessionStorage | Cookies |
|---------|-------------|----------------|---------|
| Capacity | ~5-10MB | ~5-10MB | ~4KB |
| Persistence | Until cleared | Session only | Configurable expiry |
| Sent to server | No | No | Yes (with requests) |
| Accessibility | Same origin | Same tab/origin | Same origin |
| API | Simple | Simple | String manipulation |

45. What is debouncing and throttling in JavaScript?

Debouncing: Delays function execution until after a specified time has passed since the last call
```javascript
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Usage: Search input
const searchInput = document.getElementById('search');
const debouncedSearch = debounce((event) => {
    console.log('Searching for:', event.target.value);
    // API call here
}, 300);

searchInput.addEventListener('input', debouncedSearch);

// Resize event
const debouncedResize = debounce(() => {
    console.log('Window resized');
    // Expensive layout calculations
}, 250);

window.addEventListener('resize', debouncedResize);
```

Throttling: Limits function execution to once per specified time interval
```javascript
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Usage: Scroll event
const throttledScroll = throttle(() => {
    console.log('Scroll position:', window.scrollY);
    // Update scroll indicator
}, 100);

window.addEventListener('scroll', throttledScroll);

// Button click protection
const button = document.getElementById('submit');
const throttledSubmit = throttle(() => {
    console.log('Form submitted');
    // Prevent multiple rapid submissions
}, 2000);

button.addEventListener('click', throttledSubmit);
```

Use cases:
- Debouncing: Search suggestions, form validation, resize events
- Throttling: Scroll events, mouse movement, API rate limiting

46. What are web workers in JavaScript?

Web Workers allow you to run JavaScript in background threads, separate from the main UI thread, enabling parallel processing without blocking the user interface.

Main Thread (main.js):
```javascript
// Create a new worker
const worker = new Worker('worker.js');

// Send data to worker
worker.postMessage({ command: 'start', data: [1, 2, 3, 4, 5] });

// Listen for messages from worker
worker.onmessage = function(event) {
    console.log('Result from worker:', event.data);
};

// Handle worker errors
worker.onerror = function(error) {
    console.error('Worker error:', error);
};

// Terminate worker when done
// worker.terminate();
```

Worker Thread (worker.js):
```javascript
// Listen for messages from main thread
self.onmessage = function(event) {
    const { command, data } = event.data;

    if (command === 'start') {
        // Perform heavy computation
        const result = heavyComputation(data);

        // Send result back to main thread
        self.postMessage(result);
    }
};

function heavyComputation(numbers) {
    // Simulate heavy work
    let sum = 0;
    for (let i = 0; i < 1000000000; i++) {
        sum += numbers[i % numbers.length];
    }
    return sum;
}

// Handle errors
self.onerror = function(error) {
    console.error('Error in worker:', error);
};
```

Types of Web Workers:
- Dedicated Workers: One-to-one relationship with main thread
- Shared Workers: Can be accessed by multiple scripts
- Service Workers: Act as proxy between app and network

47. What is the difference between `innerHTML` and `textContent`?

innerHTML: Gets/sets HTML content including tags
```javascript
const div = document.createElement('div');

// Setting innerHTML
div.innerHTML = '<p>Hello <strong>World</strong></p>';
console.log(div.innerHTML); // "<p>Hello <strong>World</strong></p>"

// Getting innerHTML
const container = document.getElementById('container');
console.log(container.innerHTML); // Returns HTML with tags

// Security risk with user input
const userInput = '<script>alert("XSS")</script>';
div.innerHTML = userInput; // Potentially dangerous!
```

textContent: Gets/sets only text content, strips HTML tags
```javascript
const div = document.createElement('div');

// Setting textContent
div.textContent = '<p>Hello <strong>World</strong></p>';
console.log(div.textContent); // "<p>Hello <strong>World</strong></p>" (as text)

// Getting textContent
div.innerHTML = '<p>Hello <strong>World</strong></p>';
console.log(div.textContent); // "Hello World" (tags stripped)

// Safe with user input
const userInput = '<script>alert("XSS")</script>';
div.textContent = userInput; // Safe - treated as text
```

innerText vs textContent:
```javascript
const div = document.createElement('div');
div.innerHTML = '<p style="display: none;">Hidden</p><p>Visible</p>';

console.log(div.textContent); // "HiddenVisible" (includes hidden text)
console.log(div.innerText);   // "Visible" (respects CSS styling)
```

48. How do you dynamically create HTML elements with JavaScript?

```javascript
// Create element
const div = document.createElement('div');
const p = document.createElement('p');
const img = document.createElement('img');

// Set attributes
div.id = 'myDiv';
div.className = 'container';
div.setAttribute('data-id', '123');

p.textContent = 'Hello World';
p.style.color = 'blue';

img.src = 'image.jpg';
img.alt = 'Description';

// Create with innerHTML (less safe)
const container = document.createElement('div');
container.innerHTML = `
    <h2>Title</h2>
    <p>Content</p>
    <button onclick="handleClick()">Click me</button>
`;

// Append to DOM
document.body.appendChild(div);
div.appendChild(p);

// Insert at specific position
const parent = document.getElementById('parent');
const firstChild = parent.firstElementChild;
parent.insertBefore(div, firstChild);

// Modern methods
parent.prepend(div);    // Insert at beginning
parent.append(div);     // Insert at end
div.before(p);          // Insert before div
div.after(p);           // Insert after div

// Create document fragment for multiple elements
const fragment = document.createDocumentFragment();
for (let i = 0; i < 100; i++) {
    const li = document.createElement('li');
    li.textContent = `Item ${i}`;
    fragment.appendChild(li);
}
document.getElementById('list').appendChild(fragment); // Single reflow

// Template element
const template = document.getElementById('myTemplate');
const clone = template.content.cloneNode(true);
document.body.appendChild(clone);
```

49. What is CORS and how does JavaScript handle it?

CORS (Cross-Origin Resource Sharing) is a security mechanism that allows or restricts web pages to access resources from different origins (domain, protocol, or port).

Same-Origin Policy:
```javascript
// Same origin - allowed
fetch('/api/data'); // Same domain

// Different origins - blocked by default
fetch('https://api.example.com/data'); // Different domain
fetch('http://localhost:3000/api'); // Different protocol/port
```

CORS Headers (server-side):
```javascript
// Server must include CORS headers
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*'); // or specific domain
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
});
```

Handling CORS in JavaScript:
```javascript
// Simple request (GET, POST with simple content-type)
fetch('https://api.example.com/data')
    .then(response => response.json())
    .then(data => console.log(data))
    .catch(error => {
        if (error.message.includes('CORS')) {
            console.error('CORS error - check server configuration');
        }
    });

// Preflight request (PUT, DELETE, custom headers)
fetch('https://api.example.com/data', {
    method: 'PUT',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token'
    },
    body: JSON.stringify({ data: 'value' })
});

// JSONP workaround (legacy)
function jsonpRequest(url, callback) {
    const script = document.createElement('script');
    script.src = `${url}?callback=${callback}`;
    document.head.appendChild(script);
}

// Proxy server workaround
fetch('/api/proxy?url=' + encodeURIComponent('https://api.example.com/data'));
```

50. How does JavaScript handle memory management and garbage collection?

JavaScript uses automatic memory management with garbage collection:

Memory Lifecycle:
1. Allocation: Memory is allocated when variables are created
2. Usage: Reading and writing to allocated memory
3. Release: Memory is freed when no longer needed

```javascript
// Memory allocation examples
let number = 42;           // Allocates memory for number
let string = "Hello";      // Allocates memory for string
let object = { a: 1 };     // Allocates memory for object
let array = [1, 2, 3];     // Allocates memory for array

// Memory is automatically freed when variables go out of scope
function example() {
    let localVar = "temporary"; // Allocated
    return localVar;
} // localVar memory can be freed after function execution
```

Garbage Collection Algorithms:

Reference Counting (older):
```javascript
let obj1 = { name: "Object 1" };
let obj2 = { name: "Object 2" };

obj1.ref = obj2; // obj2 reference count: 1
obj2.ref = obj1; // obj1 reference count: 1

obj1 = null; // obj1 reference count: 0, but still referenced by obj2.ref
obj2 = null; // obj2 reference count: 0, but still referenced by obj1.ref
// Circular reference - memory leak in old browsers
```

Mark-and-Sweep (modern):
```javascript
// Modern browsers use mark-and-sweep algorithm
// Marks all reachable objects from root, sweeps unmarked objects

function createObjects() {
    let parent = { name: "parent" };
    let child = { name: "child", parent: parent };
    parent.child = child; // Circular reference

    return parent;
}

let obj = createObjects();
obj = null; // Both parent and child become unreachable and will be garbage collected
```

Memory Leaks to Avoid:
```javascript
// 1. Global variables
window.myGlobalVar = new Array(1000000); // Stays in memory

// 2. Event listeners not removed
function addListener() {
    const element = document.getElementById('button');
    element.addEventListener('click', function() {
        // Handler function keeps reference to element
    });
    // Should remove listener when element is removed
}

// 3. Timers not cleared
const intervalId = setInterval(() => {
    // This keeps running and holds references
}, 1000);
// Should call clearInterval(intervalId) when done

// 4. Closures holding references
function createHandler() {
    const largeData = new Array(1000000);

    return function() {
        // This closure keeps largeData in memory
        console.log('Handler called');
    };
}

// 5. Detached DOM nodes
let detachedNode = document.getElementById('element');
detachedNode.remove(); // Removed from DOM but still referenced
detachedNode = null; // Now can be garbage collected
```

Best Practices:
- Set variables to null when done
- Remove event listeners
- Clear timers and intervals
- Avoid global variables
- Use WeakMap and WeakSet for weak references
- Monitor memory usage with browser dev tools
```

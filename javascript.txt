# JavaScript Interview Questions & Answers

## 1. Basics & Data Types

**1. What are the different data types in JavaScript?**

JavaScript has 8 data types:

**Primitive Types:**
- **Number**: Represents both integers and floating-point numbers (e.g., 42, 3.14)
- **String**: Text data enclosed in quotes (e.g., "Hello", 'World')
- **Boolean**: true or false values
- **Undefined**: Variable declared but not assigned a value
- **Null**: Intentional absence of value
- **Symbol**: Unique identifier (ES6+)
- **BigInt**: Large integers beyond Number.MAX_SAFE_INTEGER (ES2020)

**Non-Primitive Type:**
- **Object**: Complex data type including arrays, functions, objects, dates, etc.

```javascript
let num = 42;           // Number
let str = "Hello";      // String
let bool = true;        // Boolean
let undef;              // Undefined
let empty = null;       // Null
let sym = Symbol('id'); // Symbol
let big = 123n;         // BigInt
let obj = {};           // Object
```

**2. Difference between `var`, `let`, and `const`.**

| Feature | var | let | const |
|---------|-----|-----|-------|
| Scope | Function/Global | Block | Block |
| Hoisting | Yes (undefined) | Yes (TDZ) | Yes (TDZ) |
| Re-declaration | Allowed | Not allowed | Not allowed |
| Re-assignment | Allowed | Allowed | Not allowed |
| Temporal Dead Zone | No | Yes | Yes |

```javascript
// var - function scoped
function example() {
    if (true) {
        var x = 1;
    }
    console.log(x); // 1 (accessible outside block)
}

// let - block scoped
function example2() {
    if (true) {
        let y = 1;
    }
    console.log(y); // ReferenceError: y is not defined
}

// const - block scoped, immutable binding
const z = 1;
z = 2; // TypeError: Assignment to constant variable
```

**3. What is hoisting in JavaScript?**

Hoisting is JavaScript's behavior of moving variable and function declarations to the top of their scope during compilation. Only declarations are hoisted, not initializations.

```javascript
// Variable hoisting
console.log(x); // undefined (not ReferenceError)
var x = 5;

// Equivalent to:
var x;
console.log(x); // undefined
x = 5;

// Function hoisting
sayHello(); // "Hello!" (works before declaration)

function sayHello() {
    console.log("Hello!");
}

// let/const hoisting (Temporal Dead Zone)
console.log(y); // ReferenceError: Cannot access 'y' before initialization
let y = 10;
```

**4. Explain the difference between `==` and `===`.**

- **`==` (Loose Equality)**: Compares values after type coercion
- **`===` (Strict Equality)**: Compares values and types without coercion

```javascript
// Loose equality (==)
5 == "5"        // true (string "5" converted to number)
true == 1       // true (boolean converted to number)
null == undefined // true (special case)
0 == false      // true (both converted to 0)

// Strict equality (===)
5 === "5"       // false (different types)
true === 1      // false (different types)
null === undefined // false (different types)
0 === false     // false (different types)
```

**5. What is `NaN` in JavaScript and how to check for it?**

`NaN` (Not-a-Number) represents an invalid number result from mathematical operations.

```javascript
// NaN examples
let result1 = 0 / 0;        // NaN
let result2 = "hello" * 2;  // NaN
let result3 = Math.sqrt(-1); // NaN

// Checking for NaN
console.log(NaN === NaN);   // false (NaN is not equal to itself)
console.log(isNaN(NaN));    // true
console.log(Number.isNaN(NaN)); // true (preferred method)

// Difference between isNaN() and Number.isNaN()
isNaN("hello");        // true (converts to NaN first)
Number.isNaN("hello"); // false (doesn't convert, checks if actually NaN)
```

**6. How does JavaScript handle type conversion?**

JavaScript performs automatic type conversion (coercion) in certain contexts:

**Implicit Coercion:**
```javascript
// String concatenation
"5" + 3        // "53" (number to string)
"5" - 3        // 2 (string to number)
"5" * "2"      // 10 (both strings to numbers)

// Boolean context
if ("hello") { } // true (non-empty string is truthy)
if (0) { }       // false (0 is falsy)

// Comparison
"10" > 5       // true (string "10" converted to number)
```

**Explicit Conversion:**
```javascript
// To string
String(123)    // "123"
(123).toString() // "123"

// To number
Number("123")  // 123
parseInt("123px") // 123
parseFloat("12.34") // 12.34

// To boolean
Boolean(1)     // true
Boolean("")    // false
!!value        // double negation trick
```

**7. What is `typeof null` and why is it `"object"`?**

```javascript
typeof null // "object"
```

This is a well-known bug in JavaScript that exists for historical reasons. `null` should return "null", but it returns "object" due to how JavaScript was originally implemented. This behavior is maintained for backward compatibility.

**Correct way to check for null:**
```javascript
value === null              // true if null
value == null              // true if null or undefined
Object.prototype.toString.call(value) === '[object Null]' // true if null
```

**8. What is the difference between `undefined` and `null`?**

| undefined | null |
|-----------|------|
| Default value for uninitialized variables | Intentional absence of value |
| Function returns undefined if no return | Explicitly assigned by programmer |
| typeof undefined = "undefined" | typeof null = "object" |
| undefined == null (true) | null == undefined (true) |
| undefined === null (false) | null === undefined (false) |

```javascript
let a;              // undefined (declared but not assigned)
let b = null;       // null (explicitly assigned)

function test() {}  // returns undefined
let obj = { prop: null }; // null assigned intentionally

console.log(a == b);  // true (loose equality)
console.log(a === b); // false (strict equality)
```

**9. How are primitive types and reference types stored in memory?**

**Primitive Types (Stack):**
- Stored directly in memory location
- Passed by value
- Each variable has its own copy

**Reference Types (Heap):**
- Stored in heap memory
- Variable holds reference/pointer to memory location
- Passed by reference

```javascript
// Primitive types
let a = 5;
let b = a;    // b gets copy of a's value
a = 10;       // changing a doesn't affect b
console.log(b); // 5

// Reference types
let obj1 = { name: "John" };
let obj2 = obj1;  // obj2 gets reference to same object
obj1.name = "Jane"; // changing obj1 affects obj2
console.log(obj2.name); // "Jane"
```

**10. What is a template literal in JavaScript?**

Template literals are string literals that allow embedded expressions and multi-line strings using backticks (`).

```javascript
// Basic usage
const name = "John";
const age = 30;
const message = `Hello, my name is ${name} and I'm ${age} years old.`;

// Multi-line strings
const multiLine = `
    This is a
    multi-line
    string
`;

// Expression evaluation
const a = 5, b = 10;
console.log(`Sum: ${a + b}`); // "Sum: 15"

// Function calls
function greet(name) {
    return `Hello, ${name}!`;
}
console.log(`${greet("Alice")}`); // "Hello, Alice!"

// Tagged templates
function highlight(strings, ...values) {
    return strings.reduce((result, string, i) => {
        return result + string + (values[i] ? `<mark>${values[i]}</mark>` : '');
    }, '');
}

const user = "John";
const highlighted = highlight`User: ${user}`;
// "User: <mark>John</mark>"
```

## 2. Functions & Scope

**11. What are arrow functions and how are they different from regular functions?**

Arrow functions are a concise way to write functions introduced in ES6.

**Syntax:**
```javascript
// Regular function
function add(a, b) {
    return a + b;
}

// Arrow function
const add = (a, b) => a + b;

// Multiple lines
const multiply = (a, b) => {
    const result = a * b;
    return result;
};

// Single parameter (parentheses optional)
const square = x => x * x;

// No parameters
const greet = () => "Hello!";
```

**Key Differences:**

1. **`this` binding**: Arrow functions don't have their own `this`
```javascript
const obj = {
    name: "John",
    regularFunction: function() {
        console.log(this.name); // "John"
    },
    arrowFunction: () => {
        console.log(this.name); // undefined (inherits from outer scope)
    }
};
```

2. **No `arguments` object**:
```javascript
function regular() {
    console.log(arguments); // Arguments object available
}

const arrow = () => {
    console.log(arguments); // ReferenceError
};

// Use rest parameters instead
const arrowWithRest = (...args) => {
    console.log(args); // Array of arguments
};
```

3. **Cannot be used as constructors**:
```javascript
function RegularFunction() {
    this.name = "Regular";
}
const instance1 = new RegularFunction(); // Works

const ArrowFunction = () => {
    this.name = "Arrow";
};
const instance2 = new ArrowFunction(); // TypeError
```

4. **No hoisting**:
```javascript
console.log(regularFunc()); // Works due to hoisting

function regularFunc() {
    return "Regular";
}

console.log(arrowFunc()); // ReferenceError

const arrowFunc = () => "Arrow";

**12. What is the difference between function declaration and function expression?**

**Function Declaration:**
- Hoisted completely (can be called before declaration)
- Creates a named function
- Must have a function name

```javascript
// Can be called before declaration
sayHello(); // "Hello!"

function sayHello() {
    console.log("Hello!");
}
```

**Function Expression:**
- Not hoisted (cannot be called before assignment)
- Can be anonymous or named
- Assigned to a variable

```javascript
// Cannot be called before assignment
sayGoodbye(); // TypeError: sayGoodbye is not a function

var sayGoodbye = function() {
    console.log("Goodbye!");
};

// Named function expression
const namedFunc = function myFunc() {
    console.log("Named function expression");
};
```

**13. What is lexical scope in JavaScript?**

Lexical scope means that the scope of variables is determined by where they are declared in the code (lexically). Inner functions have access to variables in their outer scope.

```javascript
function outerFunction(x) {
    const outerVar = "I'm outer";

    function innerFunction(y) {
        const innerVar = "I'm inner";
        console.log(outerVar); // Can access outer variable
        console.log(x);        // Can access outer parameter
        console.log(y);        // Can access own parameter
    }

    innerFunction(20);
    // console.log(innerVar); // ReferenceError: innerVar is not defined
}

outerFunction(10);
```

**14. What is a closure and give an example.**

A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.

```javascript
// Basic closure example
function outerFunction(x) {
    return function innerFunction(y) {
        return x + y; // Inner function accesses outer variable 'x'
    };
}

const addFive = outerFunction(5);
console.log(addFive(3)); // 8 (5 + 3)

// Practical example: Counter
function createCounter() {
    let count = 0;

    return {
        increment: () => ++count,
        decrement: () => --count,
        getCount: () => count
    };
}

const counter = createCounter();
console.log(counter.increment()); // 1
console.log(counter.increment()); // 2
console.log(counter.getCount());  // 2

// Module pattern using closure
const calculator = (function() {
    let result = 0;

    return {
        add: (x) => result += x,
        subtract: (x) => result -= x,
        getResult: () => result,
        reset: () => result = 0
    };
})();
```

**15. What is the difference between synchronous and asynchronous functions?**

**Synchronous Functions:**
- Execute line by line
- Block execution until completion
- Return values immediately

```javascript
function syncFunction() {
    console.log("Start");
    for (let i = 0; i < 1000000000; i++) {
        // Blocking operation
    }
    console.log("End");
    return "Sync result";
}

const result = syncFunction(); // Blocks until completion
console.log(result);
```

**Asynchronous Functions:**
- Don't block execution
- Use callbacks, promises, or async/await
- Return immediately (often a promise)

```javascript
// Callback-based async
function asyncCallback(callback) {
    setTimeout(() => {
        callback("Async result");
    }, 1000);
}

// Promise-based async
function asyncPromise() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve("Promise result");
        }, 1000);
    });
}

// Async/await
async function asyncAwait() {
    const result = await asyncPromise();
    return result;
}
```

**16. How does `this` keyword work in JavaScript?**

The value of `this` depends on how a function is called:

**1. Global Context:**
```javascript
console.log(this); // Window object (browser) or global object (Node.js)
```

**2. Function Context:**
```javascript
function regularFunction() {
    console.log(this); // Window (non-strict) or undefined (strict mode)
}
```

**3. Object Method:**
```javascript
const obj = {
    name: "John",
    greet: function() {
        console.log(this.name); // "John" (this refers to obj)
    }
};
obj.greet();
```

**4. Constructor Function:**
```javascript
function Person(name) {
    this.name = name; // this refers to new instance
}
const person = new Person("Alice");
```

**5. Arrow Functions:**
```javascript
const obj = {
    name: "John",
    greet: () => {
        console.log(this.name); // undefined (inherits from outer scope)
    }
};
```

**6. Event Handlers:**
```javascript
button.addEventListener('click', function() {
    console.log(this); // Refers to the button element
});
```

**17. Difference between `.call()`, `.apply()`, and `.bind()`.**

These methods allow you to explicitly set the value of `this`:

**`.call()`** - Calls function with specified `this` and individual arguments:
```javascript
function greet(greeting, punctuation) {
    console.log(`${greeting}, ${this.name}${punctuation}`);
}

const person = { name: "John" };
greet.call(person, "Hello", "!"); // "Hello, John!"
```

**`.apply()`** - Same as call but takes arguments as an array:
```javascript
greet.apply(person, ["Hi", "?"]); // "Hi, John?"
```

**`.bind()`** - Returns a new function with bound `this` (doesn't call immediately):
```javascript
const boundGreet = greet.bind(person);
boundGreet("Hey", "."); // "Hey, John."

// Partial application
const sayHello = greet.bind(person, "Hello");
sayHello("!!!"); // "Hello, John!!!"
```

**18. What are default parameters in functions?**

Default parameters allow you to specify default values for function parameters:

```javascript
// ES6 default parameters
function greet(name = "Guest", greeting = "Hello") {
    console.log(`${greeting}, ${name}!`);
}

greet();                    // "Hello, Guest!"
greet("John");              // "Hello, John!"
greet("Alice", "Hi");       // "Hi, Alice!"

// Default parameters with expressions
function createUser(name, id = Date.now()) {
    return { name, id };
}

// Default parameters can reference other parameters
function calculateArea(width, height = width) {
    return width * height;
}

calculateArea(5);    // 25 (square)
calculateArea(5, 3); // 15 (rectangle)

// Default parameters with destructuring
function processUser({ name = "Anonymous", age = 0 } = {}) {
    console.log(`Name: ${name}, Age: ${age}`);
}

processUser();                        // "Name: Anonymous, Age: 0"
processUser({ name: "John" });        // "Name: John, Age: 0"
processUser({ name: "Alice", age: 25 }); // "Name: Alice, Age: 25"
```

**19. What is the difference between pure and impure functions?**

**Pure Functions:**
- Always return the same output for the same input
- No side effects (don't modify external state)
- Don't depend on external state

```javascript
// Pure function examples
function add(a, b) {
    return a + b; // Always returns same result for same inputs
}

function multiply(arr, factor) {
    return arr.map(x => x * factor); // Doesn't modify original array
}

function calculateTax(amount, rate) {
    return amount * rate; // No side effects
}
```

**Impure Functions:**
- May return different outputs for same inputs
- Have side effects (modify external state)
- Depend on external state

```javascript
// Impure function examples
let counter = 0;
function incrementCounter() {
    counter++; // Modifies external state
    return counter;
}

function addRandomNumber(x) {
    return x + Math.random(); // Different output for same input
}

function logAndReturn(value) {
    console.log(value); // Side effect (logging)
    return value;
}

const users = [];
function addUser(user) {
    users.push(user); // Modifies external array
    return users.length;
}
```

**20. Explain higher-order functions in JavaScript.**

Higher-order functions are functions that either:
1. Take other functions as arguments, or
2. Return functions as results

```javascript
// Function that takes another function as argument
function applyOperation(arr, operation) {
    return arr.map(operation);
}

const numbers = [1, 2, 3, 4, 5];
const doubled = applyOperation(numbers, x => x * 2); // [2, 4, 6, 8, 10]

// Function that returns another function
function createMultiplier(factor) {
    return function(number) {
        return number * factor;
    };
}

const double = createMultiplier(2);
const triple = createMultiplier(3);

console.log(double(5)); // 10
console.log(triple(4)); // 12

// Built-in higher-order functions
const numbers2 = [1, 2, 3, 4, 5];

// map - transforms each element
const squared = numbers2.map(x => x * x); // [1, 4, 9, 16, 25]

// filter - selects elements based on condition
const evens = numbers2.filter(x => x % 2 === 0); // [2, 4]

// reduce - accumulates values
const sum = numbers2.reduce((acc, x) => acc + x, 0); // 15

// forEach - executes function for each element
numbers2.forEach(x => console.log(x));

// Chaining higher-order functions
const result = numbers2
    .filter(x => x > 2)    // [3, 4, 5]
    .map(x => x * 2)       // [6, 8, 10]
    .reduce((acc, x) => acc + x, 0); // 24
```
```

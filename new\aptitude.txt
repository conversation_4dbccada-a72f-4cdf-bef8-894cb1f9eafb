# TOP 50 CODING APTITUDE QUESTIONS FOR MERN STACK DEVELOPERS
# Logic-Based Programming Questions with Detailed Explanations

===============================================================================
SECTION 1: ARRAY & STRING MANIPULATION (Questions 1-15)
===============================================================================

1. Find the missing number in an array of 1 to n.

```javascript
function findMissingNumber(arr, n) {
    const expectedSum = (n * (n + 1)) / 2;
    const actualSum = arr.reduce((sum, num) => sum + num, 0);
    return expectedSum - actualSum;
}

// Example: [1, 2, 4, 5], n=5 → Missing: 3
```
Explanation: Use the mathematical formula for sum of first n natural numbers. The difference between expected and actual sum gives the missing number. Time complexity: O(n), Space: O(1).

2. Find two numbers in an array that add up to a target sum.

```javascript
function twoSum(nums, target) {
    const map = new Map();
    
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
    
    return [];
}

// Example: [2, 7, 11, 15], target=9 → [0, 1]
```
Explanation: Use a hash map to store numbers and their indices. For each number, check if its complement (target - current) exists in the map. This avoids nested loops and reduces time complexity from O(n²) to O(n).

3. Rotate an array to the right by k steps.

```javascript
function rotateArray(nums, k) {
    const n = nums.length;
    k = k % n; // Handle cases where k > n
    
    // Reverse entire array
    reverse(nums, 0, n - 1);
    // Reverse first k elements
    reverse(nums, 0, k - 1);
    // Reverse remaining elements
    reverse(nums, k, n - 1);
    
    return nums;
}

function reverse(arr, start, end) {
    while (start < end) {
        [arr[start], arr[end]] = [arr[end], arr[start]];
        start++;
        end--;
    }
}

// Example: [1,2,3,4,5], k=2 → [4,5,1,2,3]
```
Explanation: Three-step reversal algorithm. First reverse the entire array, then reverse the first k elements, then reverse the remaining elements. This achieves rotation in O(n) time with O(1) space.

4. Find the longest substring without repeating characters.

```javascript
function lengthOfLongestSubstring(s) {
    const charSet = new Set();
    let left = 0;
    let maxLength = 0;
    
    for (let right = 0; right < s.length; right++) {
        while (charSet.has(s[right])) {
            charSet.delete(s[left]);
            left++;
        }
        charSet.add(s[right]);
        maxLength = Math.max(maxLength, right - left + 1);
    }
    
    return maxLength;
}

// Example: "abcabcbb" → 3 (substring "abc")
```
Explanation: Sliding window technique with two pointers. Expand the window by moving the right pointer and shrink it by moving the left pointer when duplicates are found. The set keeps track of characters in the current window.

5. Merge two sorted arrays into one sorted array.

```javascript
function mergeSortedArrays(arr1, arr2) {
    const merged = [];
    let i = 0, j = 0;
    
    while (i < arr1.length && j < arr2.length) {
        if (arr1[i] <= arr2[j]) {
            merged.push(arr1[i++]);
        } else {
            merged.push(arr2[j++]);
        }
    }
    
    // Add remaining elements
    while (i < arr1.length) merged.push(arr1[i++]);
    while (j < arr2.length) merged.push(arr2[j++]);
    
    return merged;
}

// Example: [1,3,5], [2,4,6] → [1,2,3,4,5,6]
```
Explanation: Two-pointer technique comparing elements from both arrays. Always pick the smaller element and advance that pointer. After one array is exhausted, append remaining elements from the other array.

6. Find the first non-repeating character in a string.

```javascript
function firstNonRepeatingChar(s) {
    const charCount = {};
    
    // Count frequency of each character
    for (let char of s) {
        charCount[char] = (charCount[char] || 0) + 1;
    }
    
    // Find first character with count 1
    for (let i = 0; i < s.length; i++) {
        if (charCount[s[i]] === 1) {
            return i;
        }
    }
    
    return -1;
}

// Example: "leetcode" → 0 (character 'l')
```
Explanation: Two-pass algorithm. First pass counts frequency of each character using a hash map. Second pass finds the first character with frequency 1. Time complexity: O(n), Space: O(1) for limited character set.

7. Check if one string is a rotation of another.

```javascript
function isRotation(s1, s2) {
    if (s1.length !== s2.length) return false;
    
    const concatenated = s1 + s1;
    return concatenated.includes(s2);
}

// Example: "waterbottle", "erbottlewat" → true
```
Explanation: If s2 is a rotation of s1, then s2 will be a substring of s1+s1. This works because concatenating s1 with itself contains all possible rotations of s1 as substrings.

8. Find the maximum product of two integers in an array.

```javascript
function maxProduct(nums) {
    if (nums.length < 2) return 0;
    
    let max1 = -Infinity, max2 = -Infinity;
    let min1 = Infinity, min2 = Infinity;
    
    for (let num of nums) {
        if (num > max1) {
            max2 = max1;
            max1 = num;
        } else if (num > max2) {
            max2 = num;
        }
        
        if (num < min1) {
            min2 = min1;
            min1 = num;
        } else if (num < min2) {
            min2 = num;
        }
    }
    
    return Math.max(max1 * max2, min1 * min2);
}

// Example: [3, 4, 1, 2] → 12 (3 * 4)
```
Explanation: Track the two largest and two smallest numbers. Maximum product can be either from two largest positive numbers or two smallest negative numbers. Single pass solution with O(n) time complexity.

9. Remove duplicates from a sorted array in-place.

```javascript
function removeDuplicates(nums) {
    if (nums.length === 0) return 0;
    
    let writeIndex = 1;
    
    for (let readIndex = 1; readIndex < nums.length; readIndex++) {
        if (nums[readIndex] !== nums[readIndex - 1]) {
            nums[writeIndex] = nums[readIndex];
            writeIndex++;
        }
    }
    
    return writeIndex;
}

// Example: [1,1,2,2,3] → [1,2,3] with length 3
```
Explanation: Two-pointer technique with read and write pointers. Read pointer scans the array, write pointer tracks position for next unique element. Since array is sorted, duplicates are adjacent.

10. Find the intersection of two arrays.

```javascript
function intersection(nums1, nums2) {
    const set1 = new Set(nums1);
    const result = new Set();
    
    for (let num of nums2) {
        if (set1.has(num)) {
            result.add(num);
        }
    }
    
    return Array.from(result);
}

// Example: [1,2,2,1], [2,2] → [2]
```
Explanation: Convert first array to a set for O(1) lookup. Iterate through second array and check membership in the set. Use result set to avoid duplicates in output.

11. Find the majority element (appears more than n/2 times).

```javascript
function majorityElement(nums) {
    let candidate = nums[0];
    let count = 1;
    
    // Boyer-Moore Voting Algorithm
    for (let i = 1; i < nums.length; i++) {
        if (nums[i] === candidate) {
            count++;
        } else {
            count--;
            if (count === 0) {
                candidate = nums[i];
                count = 1;
            }
        }
    }
    
    return candidate;
}

// Example: [3,2,3] → 3
```
Explanation: Boyer-Moore Voting Algorithm. The idea is that if an element appears more than n/2 times, it will survive the cancellation process. Each occurrence of the majority element cancels out one occurrence of any other element.

12. Move all zeros to the end of array while maintaining relative order.

```javascript
function moveZeros(nums) {
    let writeIndex = 0;
    
    // Move all non-zero elements to the front
    for (let readIndex = 0; readIndex < nums.length; readIndex++) {
        if (nums[readIndex] !== 0) {
            nums[writeIndex] = nums[readIndex];
            writeIndex++;
        }
    }
    
    // Fill remaining positions with zeros
    while (writeIndex < nums.length) {
        nums[writeIndex] = 0;
        writeIndex++;
    }
    
    return nums;
}

// Example: [0,1,0,3,12] → [1,3,12,0,0]
```
Explanation: Two-pass algorithm. First pass moves all non-zero elements to the front while maintaining their relative order. Second pass fills remaining positions with zeros.

13. Find the peak element in an array.

```javascript
function findPeakElement(nums) {
    let left = 0;
    let right = nums.length - 1;
    
    while (left < right) {
        const mid = Math.floor((left + right) / 2);
        
        if (nums[mid] > nums[mid + 1]) {
            right = mid;
        } else {
            left = mid + 1;
        }
    }
    
    return left;
}

// Example: [1,2,3,1] → 2 (index of peak element 3)
```
Explanation: Binary search approach. A peak element is greater than its neighbors. If middle element is greater than its right neighbor, peak must be on the left side (including middle). Otherwise, peak is on the right side.

14. Find the single number in an array where every other number appears twice.

```javascript
function singleNumber(nums) {
    let result = 0;
    
    for (let num of nums) {
        result ^= num;
    }
    
    return result;
}

// Example: [2,2,1] → 1
```
Explanation: XOR operation has the property that a ^ a = 0 and a ^ 0 = a. When we XOR all numbers, pairs cancel out (become 0), leaving only the single number. This works in O(n) time with O(1) space.

15. Check if array contains duplicate within k distance.

```javascript
function containsNearbyDuplicate(nums, k) {
    const numMap = new Map();
    
    for (let i = 0; i < nums.length; i++) {
        if (numMap.has(nums[i])) {
            if (i - numMap.get(nums[i]) <= k) {
                return true;
            }
        }
        numMap.set(nums[i], i);
    }
    
    return false;
}

// Example: [1,2,3,1], k=3 → true
```
Explanation: Use hash map to store the most recent index of each number. When we encounter a number again, check if the distance from its previous occurrence is within k. Update the index for future comparisons.

===============================================================================
SECTION 2: MATHEMATICAL & LOGICAL PROBLEMS (Questions 16-25)
===============================================================================

16. Check if a number is prime.

```javascript
function isPrime(n) {
    if (n <= 1) return false;
    if (n <= 3) return true;
    if (n % 2 === 0 || n % 3 === 0) return false;

    for (let i = 5; i * i <= n; i += 6) {
        if (n % i === 0 || n % (i + 2) === 0) {
            return false;
        }
    }

    return true;
}

// Example: isPrime(17) → true
```
Explanation: Optimized primality test. Check divisibility by 2 and 3 first. Then check only numbers of form 6k±1 up to √n, since all primes > 3 are of this form. This reduces time complexity significantly.

17. Find the greatest common divisor (GCD) of two numbers.

```javascript
function gcd(a, b) {
    while (b !== 0) {
        const temp = b;
        b = a % b;
        a = temp;
    }
    return a;
}

// Recursive version
function gcdRecursive(a, b) {
    return b === 0 ? a : gcdRecursive(b, a % b);
}

// Example: gcd(48, 18) → 6
```
Explanation: Euclidean algorithm based on the principle that gcd(a,b) = gcd(b, a%b). Continue until one number becomes 0. The other number is the GCD. Time complexity: O(log(min(a,b))).

18. Generate Fibonacci sequence up to n terms.

```javascript
function fibonacci(n) {
    if (n <= 0) return [];
    if (n === 1) return [0];

    const fib = [0, 1];

    for (let i = 2; i < n; i++) {
        fib[i] = fib[i - 1] + fib[i - 2];
    }

    return fib;
}

// Space-optimized version for nth Fibonacci number
function nthFibonacci(n) {
    if (n <= 1) return n;

    let a = 0, b = 1;
    for (let i = 2; i <= n; i++) {
        [a, b] = [b, a + b];
    }

    return b;
}

// Example: fibonacci(7) → [0,1,1,2,3,5,8]
```
Explanation: Iterative approach avoids recursion overhead. For space optimization, only keep track of last two numbers instead of entire sequence. Time: O(n), Space: O(1) for nth number.

19. Check if a number is a power of two.

```javascript
function isPowerOfTwo(n) {
    return n > 0 && (n & (n - 1)) === 0;
}

// Alternative approach
function isPowerOfTwoAlternative(n) {
    if (n <= 0) return false;

    while (n % 2 === 0) {
        n /= 2;
    }

    return n === 1;
}

// Example: isPowerOfTwo(16) → true
```
Explanation: Bit manipulation trick. Powers of 2 have only one bit set. When we subtract 1 from a power of 2, all bits after the set bit become 1, and the set bit becomes 0. AND operation with original number gives 0.

20. Find all prime numbers up to n (Sieve of Eratosthenes).

```javascript
function sieveOfEratosthenes(n) {
    const isPrime = new Array(n + 1).fill(true);
    isPrime[0] = isPrime[1] = false;

    for (let i = 2; i * i <= n; i++) {
        if (isPrime[i]) {
            for (let j = i * i; j <= n; j += i) {
                isPrime[j] = false;
            }
        }
    }

    return isPrime.map((prime, index) => prime ? index : null)
                  .filter(num => num !== null);
}

// Example: sieveOfEratosthenes(10) → [2,3,5,7]
```
Explanation: Mark multiples of each prime as composite. Start marking from i² because smaller multiples would have been marked by smaller primes. This algorithm efficiently finds all primes up to n in O(n log log n) time.

21. Calculate factorial of a number.

```javascript
function factorial(n) {
    if (n < 0) return undefined;
    if (n <= 1) return 1;

    let result = 1;
    for (let i = 2; i <= n; i++) {
        result *= i;
    }

    return result;
}

// Recursive version
function factorialRecursive(n) {
    if (n < 0) return undefined;
    if (n <= 1) return 1;
    return n * factorialRecursive(n - 1);
}

// Example: factorial(5) → 120
```
Explanation: Iterative approach is more efficient than recursive for large numbers as it avoids function call overhead and stack overflow. For very large numbers, consider using BigInt in JavaScript.

22. Find the square root of a number without using built-in functions.

```javascript
function sqrt(x) {
    if (x < 0) return NaN;
    if (x === 0 || x === 1) return x;

    let left = 0;
    let right = x;
    let result = 0;

    while (left <= right) {
        const mid = Math.floor((left + right) / 2);

        if (mid * mid === x) {
            return mid;
        } else if (mid * mid < x) {
            left = mid + 1;
            result = mid;
        } else {
            right = mid - 1;
        }
    }

    return result;
}

// Example: sqrt(8) → 2 (integer square root)
```
Explanation: Binary search approach. Search for the largest integer whose square is less than or equal to x. The search space is from 0 to x, and we narrow it down by comparing mid² with x.

23. Check if a number is palindrome without converting to string.

```javascript
function isPalindromeNumber(x) {
    if (x < 0) return false;

    let original = x;
    let reversed = 0;

    while (x > 0) {
        reversed = reversed * 10 + x % 10;
        x = Math.floor(x / 10);
    }

    return original === reversed;
}

// Example: isPalindromeNumber(121) → true
```
Explanation: Reverse the number by extracting digits from right to left and building the reversed number. Compare with original. This avoids string conversion and works purely with mathematical operations.

24. Find the sum of digits of a number.

```javascript
function sumOfDigits(n) {
    let sum = 0;
    n = Math.abs(n); // Handle negative numbers

    while (n > 0) {
        sum += n % 10;
        n = Math.floor(n / 10);
    }

    return sum;
}

// Recursive version
function sumOfDigitsRecursive(n) {
    n = Math.abs(n);
    if (n === 0) return 0;
    return (n % 10) + sumOfDigitsRecursive(Math.floor(n / 10));
}

// Example: sumOfDigits(123) → 6
```
Explanation: Extract each digit using modulo 10 operation and add to sum. Divide by 10 to remove the last digit. Continue until number becomes 0. Time complexity: O(log n) where n is the input number.

25. Convert decimal to binary.

```javascript
function decimalToBinary(decimal) {
    if (decimal === 0) return "0";

    let binary = "";

    while (decimal > 0) {
        binary = (decimal % 2) + binary;
        decimal = Math.floor(decimal / 2);
    }

    return binary;
}

// Using built-in method for comparison
function decimalToBinaryBuiltIn(decimal) {
    return decimal.toString(2);
}

// Example: decimalToBinary(10) → "1010"
```
Explanation: Repeatedly divide by 2 and collect remainders. The remainders in reverse order give the binary representation. This is the standard algorithm for base conversion.

===============================================================================
SECTION 3: RECURSION & DYNAMIC PROGRAMMING (Questions 26-35)
===============================================================================

26. Calculate power of a number using recursion.

```javascript
function power(base, exponent) {
    if (exponent === 0) return 1;
    if (exponent === 1) return base;

    if (exponent % 2 === 0) {
        const half = power(base, exponent / 2);
        return half * half;
    } else {
        return base * power(base, exponent - 1);
    }
}

// Example: power(2, 10) → 1024
```
Explanation: Optimized recursive approach using exponentiation by squaring. For even exponents, calculate power(base, exp/2) once and square it. This reduces time complexity from O(n) to O(log n).

27. Find the nth term of Fibonacci sequence using memoization.

```javascript
function fibonacciMemo(n, memo = {}) {
    if (n in memo) return memo[n];
    if (n <= 1) return n;

    memo[n] = fibonacciMemo(n - 1, memo) + fibonacciMemo(n - 2, memo);
    return memo[n];
}

// Bottom-up dynamic programming approach
function fibonacciDP(n) {
    if (n <= 1) return n;

    const dp = [0, 1];
    for (let i = 2; i <= n; i++) {
        dp[i] = dp[i - 1] + dp[i - 2];
    }

    return dp[n];
}

// Example: fibonacciMemo(10) → 55
```
Explanation: Memoization stores previously calculated results to avoid redundant calculations. This reduces time complexity from O(2^n) to O(n). Bottom-up DP builds solution iteratively from smaller subproblems.

28. Count the number of ways to climb n stairs (1 or 2 steps at a time).

```javascript
function climbStairs(n) {
    if (n <= 2) return n;

    let prev2 = 1; // ways to reach step 1
    let prev1 = 2; // ways to reach step 2

    for (let i = 3; i <= n; i++) {
        const current = prev1 + prev2;
        prev2 = prev1;
        prev1 = current;
    }

    return prev1;
}

// Recursive with memoization
function climbStairsMemo(n, memo = {}) {
    if (n in memo) return memo[n];
    if (n <= 2) return n;

    memo[n] = climbStairsMemo(n - 1, memo) + climbStairsMemo(n - 2, memo);
    return memo[n];
}

// Example: climbStairs(5) → 8
```
Explanation: This is essentially Fibonacci sequence. To reach step n, you can come from step (n-1) with 1 step or from step (n-2) with 2 steps. Total ways = ways(n-1) + ways(n-2).

29. Find the longest common subsequence of two strings.

```javascript
function longestCommonSubsequence(text1, text2) {
    const m = text1.length;
    const n = text2.length;
    const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

    for (let i = 1; i <= m; i++) {
        for (let j = 1; j <= n; j++) {
            if (text1[i - 1] === text2[j - 1]) {
                dp[i][j] = dp[i - 1][j - 1] + 1;
            } else {
                dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
            }
        }
    }

    return dp[m][n];
}

// Example: longestCommonSubsequence("abcde", "ace") → 3
```
Explanation: Dynamic programming solution. If characters match, add 1 to diagonal value. If they don't match, take maximum of top or left cell. This builds up the solution from smaller subproblems.

30. Calculate minimum number of coins to make a given amount.

```javascript
function coinChange(coins, amount) {
    const dp = Array(amount + 1).fill(Infinity);
    dp[0] = 0;

    for (let i = 1; i <= amount; i++) {
        for (let coin of coins) {
            if (coin <= i) {
                dp[i] = Math.min(dp[i], dp[i - coin] + 1);
            }
        }
    }

    return dp[amount] === Infinity ? -1 : dp[amount];
}

// Example: coinChange([1, 3, 4], 6) → 2 (3 + 3)
```
Explanation: Bottom-up DP approach. For each amount, try all coins and take the minimum. dp[i] represents minimum coins needed for amount i. If we use coin c, we need dp[i-c] + 1 coins total.

31. Generate all permutations of a string.

```javascript
function permutations(str) {
    if (str.length <= 1) return [str];

    const result = [];

    for (let i = 0; i < str.length; i++) {
        const char = str[i];
        const remaining = str.slice(0, i) + str.slice(i + 1);
        const perms = permutations(remaining);

        for (let perm of perms) {
            result.push(char + perm);
        }
    }

    return result;
}

// Example: permutations("abc") → ["abc", "acb", "bac", "bca", "cab", "cba"]
```
Explanation: Recursive approach. For each character, fix it at the beginning and find permutations of remaining characters. Combine the fixed character with each permutation of the rest.

32. Check if a string is a valid palindrome using recursion.

```javascript
function isPalindromeRecursive(str, left = 0, right = str.length - 1) {
    // Base case: single character or empty string
    if (left >= right) return true;

    // If characters don't match
    if (str[left] !== str[right]) return false;

    // Recursively check inner substring
    return isPalindromeRecursive(str, left + 1, right - 1);
}

// Example: isPalindromeRecursive("racecar") → true
```
Explanation: Compare characters from both ends moving inward. If any pair doesn't match, it's not a palindrome. Base case is when pointers meet or cross, indicating all characters matched.

33. Find the maximum sum of non-adjacent elements in an array.

```javascript
function maxNonAdjacentSum(nums) {
    if (nums.length === 0) return 0;
    if (nums.length === 1) return nums[0];

    let include = nums[0]; // Max sum including previous element
    let exclude = 0;       // Max sum excluding previous element

    for (let i = 1; i < nums.length; i++) {
        const newExclude = Math.max(include, exclude);
        include = exclude + nums[i];
        exclude = newExclude;
    }

    return Math.max(include, exclude);
}

// Example: maxNonAdjacentSum([2, 1, 4, 9]) → 11 (2 + 9)
```
Explanation: For each element, we have two choices: include it (add to exclude) or exclude it (take max of previous include/exclude). This ensures no two adjacent elements are selected.

34. Count the number of paths in a grid from top-left to bottom-right.

```javascript
function uniquePaths(m, n) {
    const dp = Array(m).fill().map(() => Array(n).fill(1));

    for (let i = 1; i < m; i++) {
        for (let j = 1; j < n; j++) {
            dp[i][j] = dp[i - 1][j] + dp[i][j - 1];
        }
    }

    return dp[m - 1][n - 1];
}

// Space-optimized version
function uniquePathsOptimized(m, n) {
    let dp = Array(n).fill(1);

    for (let i = 1; i < m; i++) {
        for (let j = 1; j < n; j++) {
            dp[j] += dp[j - 1];
        }
    }

    return dp[n - 1];
}

// Example: uniquePaths(3, 2) → 3
```
Explanation: Dynamic programming solution. Number of paths to reach any cell is sum of paths from top cell and left cell. First row and column have only one path each (all right or all down).

35. Find the edit distance between two strings.

```javascript
function editDistance(str1, str2) {
    const m = str1.length;
    const n = str2.length;
    const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

    // Initialize base cases
    for (let i = 0; i <= m; i++) dp[i][0] = i;
    for (let j = 0; j <= n; j++) dp[0][j] = j;

    for (let i = 1; i <= m; i++) {
        for (let j = 1; j <= n; j++) {
            if (str1[i - 1] === str2[j - 1]) {
                dp[i][j] = dp[i - 1][j - 1];
            } else {
                dp[i][j] = 1 + Math.min(
                    dp[i - 1][j],     // deletion
                    dp[i][j - 1],     // insertion
                    dp[i - 1][j - 1]  // substitution
                );
            }
        }
    }

    return dp[m][n];
}

// Example: editDistance("kitten", "sitting") → 3
```
Explanation: Levenshtein distance using DP. If characters match, no operation needed. Otherwise, take minimum of three operations: insert, delete, or substitute. Each operation costs 1.

===============================================================================
SECTION 4: SORTING & SEARCHING ALGORITHMS (Questions 36-45)
===============================================================================

36. Implement binary search algorithm.

```javascript
function binarySearch(arr, target) {
    let left = 0;
    let right = arr.length - 1;

    while (left <= right) {
        const mid = Math.floor((left + right) / 2);

        if (arr[mid] === target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    return -1;
}

// Recursive version
function binarySearchRecursive(arr, target, left = 0, right = arr.length - 1) {
    if (left > right) return -1;

    const mid = Math.floor((left + right) / 2);

    if (arr[mid] === target) return mid;
    if (arr[mid] < target) return binarySearchRecursive(arr, target, mid + 1, right);
    return binarySearchRecursive(arr, target, left, mid - 1);
}

// Example: binarySearch([1,3,5,7,9], 5) → 2
```
Explanation: Divide and conquer algorithm for sorted arrays. Compare target with middle element and eliminate half of the search space. Time complexity: O(log n), Space: O(1) for iterative version.

37. Implement quick sort algorithm.

```javascript
function quickSort(arr) {
    if (arr.length <= 1) return arr;

    const pivot = arr[Math.floor(arr.length / 2)];
    const left = arr.filter(x => x < pivot);
    const middle = arr.filter(x => x === pivot);
    const right = arr.filter(x => x > pivot);

    return [...quickSort(left), ...middle, ...quickSort(right)];
}

// In-place version
function quickSortInPlace(arr, low = 0, high = arr.length - 1) {
    if (low < high) {
        const pivotIndex = partition(arr, low, high);
        quickSortInPlace(arr, low, pivotIndex - 1);
        quickSortInPlace(arr, pivotIndex + 1, high);
    }
    return arr;
}

function partition(arr, low, high) {
    const pivot = arr[high];
    let i = low - 1;

    for (let j = low; j < high; j++) {
        if (arr[j] <= pivot) {
            i++;
            [arr[i], arr[j]] = [arr[j], arr[i]];
        }
    }

    [arr[i + 1], arr[high]] = [arr[high], arr[i + 1]];
    return i + 1;
}

// Example: quickSort([3,6,8,10,1,2,1]) → [1,1,2,3,6,8,10]
```
Explanation: Divide and conquer algorithm. Choose a pivot, partition array so elements smaller than pivot are on left, larger on right. Recursively sort both parts. Average time: O(n log n), Worst: O(n²).

38. Find the kth largest element in an array.

```javascript
function findKthLargest(nums, k) {
    // Quick select algorithm
    function quickSelect(left, right, kSmallest) {
        if (left === right) return nums[left];

        const pivotIndex = partition(left, right);

        if (kSmallest === pivotIndex) {
            return nums[kSmallest];
        } else if (kSmallest < pivotIndex) {
            return quickSelect(left, pivotIndex - 1, kSmallest);
        } else {
            return quickSelect(pivotIndex + 1, right, kSmallest);
        }
    }

    function partition(left, right) {
        const pivot = nums[right];
        let i = left;

        for (let j = left; j < right; j++) {
            if (nums[j] <= pivot) {
                [nums[i], nums[j]] = [nums[j], nums[i]];
                i++;
            }
        }

        [nums[i], nums[right]] = [nums[right], nums[i]];
        return i;
    }

    return quickSelect(0, nums.length - 1, nums.length - k);
}

// Example: findKthLargest([3,2,1,5,6,4], 2) → 5
```
Explanation: Quick select algorithm, similar to quick sort but only recurse on the side containing the kth element. Average time complexity: O(n), worst case: O(n²). More efficient than sorting entire array.

39. Merge sort implementation.

```javascript
function mergeSort(arr) {
    if (arr.length <= 1) return arr;

    const mid = Math.floor(arr.length / 2);
    const left = mergeSort(arr.slice(0, mid));
    const right = mergeSort(arr.slice(mid));

    return merge(left, right);
}

function merge(left, right) {
    const result = [];
    let i = 0, j = 0;

    while (i < left.length && j < right.length) {
        if (left[i] <= right[j]) {
            result.push(left[i++]);
        } else {
            result.push(right[j++]);
        }
    }

    return result.concat(left.slice(i)).concat(right.slice(j));
}

// Example: mergeSort([38,27,43,3,9,82,10]) → [3,9,10,27,38,43,82]
```
Explanation: Divide and conquer stable sorting algorithm. Recursively divide array into halves until single elements, then merge them back in sorted order. Time complexity: O(n log n) in all cases, Space: O(n).

40. Search in a rotated sorted array.

```javascript
function searchRotatedArray(nums, target) {
    let left = 0;
    let right = nums.length - 1;

    while (left <= right) {
        const mid = Math.floor((left + right) / 2);

        if (nums[mid] === target) return mid;

        // Check which half is sorted
        if (nums[left] <= nums[mid]) {
            // Left half is sorted
            if (nums[left] <= target && target < nums[mid]) {
                right = mid - 1;
            } else {
                left = mid + 1;
            }
        } else {
            // Right half is sorted
            if (nums[mid] < target && target <= nums[right]) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
    }

    return -1;
}

// Example: searchRotatedArray([4,5,6,7,0,1,2], 0) → 4
```
Explanation: Modified binary search. At each step, determine which half is sorted by comparing with endpoints. If target is in the sorted half's range, search there; otherwise, search the other half.

41. Find the first and last position of element in sorted array.

```javascript
function searchRange(nums, target) {
    function findFirst(nums, target) {
        let left = 0, right = nums.length - 1;
        let result = -1;

        while (left <= right) {
            const mid = Math.floor((left + right) / 2);

            if (nums[mid] === target) {
                result = mid;
                right = mid - 1; // Continue searching left
            } else if (nums[mid] < target) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return result;
    }

    function findLast(nums, target) {
        let left = 0, right = nums.length - 1;
        let result = -1;

        while (left <= right) {
            const mid = Math.floor((left + right) / 2);

            if (nums[mid] === target) {
                result = mid;
                left = mid + 1; // Continue searching right
            } else if (nums[mid] < target) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return result;
    }

    return [findFirst(nums, target), findLast(nums, target)];
}

// Example: searchRange([5,7,7,8,8,10], 8) → [3,4]
```
Explanation: Two binary searches. First finds the leftmost occurrence by continuing to search left even after finding target. Second finds rightmost occurrence by continuing to search right after finding target.

42. Find peak element in array (element greater than neighbors).

```javascript
function findPeakElement(nums) {
    let left = 0;
    let right = nums.length - 1;

    while (left < right) {
        const mid = Math.floor((left + right) / 2);

        if (nums[mid] > nums[mid + 1]) {
            // Peak is on the left side (including mid)
            right = mid;
        } else {
            // Peak is on the right side
            left = mid + 1;
        }
    }

    return left;
}

// Example: findPeakElement([1,2,3,1]) → 2
```
Explanation: Binary search approach. If middle element is greater than its right neighbor, peak must be on the left side. Otherwise, peak is on the right side. This works because array boundaries are considered as negative infinity.

43. Sort colors (Dutch National Flag problem).

```javascript
function sortColors(nums) {
    let low = 0;    // Boundary for 0s
    let mid = 0;    // Current element
    let high = nums.length - 1; // Boundary for 2s

    while (mid <= high) {
        if (nums[mid] === 0) {
            [nums[low], nums[mid]] = [nums[mid], nums[low]];
            low++;
            mid++;
        } else if (nums[mid] === 1) {
            mid++;
        } else { // nums[mid] === 2
            [nums[mid], nums[high]] = [nums[high], nums[mid]];
            high--;
            // Don't increment mid as we need to check swapped element
        }
    }

    return nums;
}

// Example: sortColors([2,0,2,1,1,0]) → [0,0,1,1,2,2]
```
Explanation: Three-pointer technique. Maintain boundaries for 0s, 1s, and 2s. Swap elements to their correct regions. Single pass algorithm with O(n) time and O(1) space complexity.

44. Find minimum in rotated sorted array.

```javascript
function findMin(nums) {
    let left = 0;
    let right = nums.length - 1;

    while (left < right) {
        const mid = Math.floor((left + right) / 2);

        if (nums[mid] > nums[right]) {
            // Minimum is in right half
            left = mid + 1;
        } else {
            // Minimum is in left half (including mid)
            right = mid;
        }
    }

    return nums[left];
}

// With duplicates
function findMinWithDuplicates(nums) {
    let left = 0;
    let right = nums.length - 1;

    while (left < right) {
        const mid = Math.floor((left + right) / 2);

        if (nums[mid] > nums[right]) {
            left = mid + 1;
        } else if (nums[mid] < nums[right]) {
            right = mid;
        } else {
            // nums[mid] === nums[right], can't determine which side
            right--;
        }
    }

    return nums[left];
}

// Example: findMin([3,4,5,1,2]) → 1
```
Explanation: Binary search on rotated array. Compare middle with rightmost element. If mid > right, minimum is in right half. Otherwise, minimum is in left half including mid. Handle duplicates by decrementing right pointer.

45. Search for a range in sorted array.

```javascript
function searchInsert(nums, target) {
    let left = 0;
    let right = nums.length - 1;

    while (left <= right) {
        const mid = Math.floor((left + right) / 2);

        if (nums[mid] === target) {
            return mid;
        } else if (nums[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    return left; // Insert position
}

// Example: searchInsert([1,3,5,6], 5) → 2
// Example: searchInsert([1,3,5,6], 2) → 1
```
Explanation: Modified binary search. If target is found, return its index. If not found, left pointer will be at the correct insertion position to maintain sorted order. This works because left moves right when we need to insert after current position.

===============================================================================
SECTION 5: TREE & GRAPH PROBLEMS (Questions 46-50)
===============================================================================

46. Check if a binary tree is balanced.

```javascript
function isBalanced(root) {
    function checkHeight(node) {
        if (!node) return 0;

        const leftHeight = checkHeight(node.left);
        if (leftHeight === -1) return -1; // Left subtree is unbalanced

        const rightHeight = checkHeight(node.right);
        if (rightHeight === -1) return -1; // Right subtree is unbalanced

        // Check if current node is balanced
        if (Math.abs(leftHeight - rightHeight) > 1) return -1;

        return Math.max(leftHeight, rightHeight) + 1;
    }

    return checkHeight(root) !== -1;
}

// Example: Tree [3,9,20,null,null,15,7] → true
```
Explanation: Post-order traversal that calculates height and checks balance simultaneously. If any subtree is unbalanced, return -1 to propagate the unbalanced state. A tree is balanced if height difference between left and right subtrees is at most 1.

47. Find the lowest common ancestor of two nodes in binary tree.

```javascript
function lowestCommonAncestor(root, p, q) {
    if (!root || root === p || root === q) {
        return root;
    }

    const left = lowestCommonAncestor(root.left, p, q);
    const right = lowestCommonAncestor(root.right, p, q);

    // If both left and right are not null, current node is LCA
    if (left && right) return root;

    // Return the non-null child (or null if both are null)
    return left || right;
}

// Example: Tree [3,5,1,6,2,0,8,null,null,7,4], p=5, q=1 → 3
```
Explanation: Recursive approach. If current node is one of the target nodes or null, return it. If both left and right subtrees return non-null values, current node is the LCA. Otherwise, return the non-null subtree result.

48. Validate if a binary tree is a valid binary search tree.

```javascript
function isValidBST(root) {
    function validate(node, min, max) {
        if (!node) return true;

        if (node.val <= min || node.val >= max) {
            return false;
        }

        return validate(node.left, min, node.val) &&
               validate(node.right, node.val, max);
    }

    return validate(root, -Infinity, Infinity);
}

// Inorder traversal approach
function isValidBSTInorder(root) {
    let prev = -Infinity;

    function inorder(node) {
        if (!node) return true;

        if (!inorder(node.left)) return false;

        if (node.val <= prev) return false;
        prev = node.val;

        return inorder(node.right);
    }

    return inorder(root);
}

// Example: Tree [2,1,3] → true, Tree [5,1,4,null,null,3,6] → false
```
Explanation: Two approaches: 1) Pass valid range (min, max) for each node. 2) Inorder traversal should give sorted sequence for valid BST. Both ensure all nodes satisfy BST property with their ancestors.

49. Find all paths from root to leaves in binary tree.

```javascript
function binaryTreePaths(root) {
    const result = [];

    function dfs(node, path) {
        if (!node) return;

        path.push(node.val);

        // If it's a leaf node, add path to result
        if (!node.left && !node.right) {
            result.push(path.join('->'));
        } else {
            // Continue DFS for children
            dfs(node.left, path);
            dfs(node.right, path);
        }

        // Backtrack
        path.pop();
    }

    dfs(root, []);
    return result;
}

// Example: Tree [1,2,3,null,5] → ["1->2->5", "1->3"]
```
Explanation: Depth-first search with backtracking. Maintain current path and add to result when reaching a leaf. Backtrack by removing current node from path when returning from recursion to explore other paths.

50. Implement breadth-first search (BFS) for graph traversal.

```javascript
function bfsGraph(graph, start) {
    const visited = new Set();
    const queue = [start];
    const result = [];

    visited.add(start);

    while (queue.length > 0) {
        const node = queue.shift();
        result.push(node);

        // Visit all unvisited neighbors
        for (let neighbor of graph[node] || []) {
            if (!visited.has(neighbor)) {
                visited.add(neighbor);
                queue.push(neighbor);
            }
        }
    }

    return result;
}

// For binary tree level order traversal
function levelOrder(root) {
    if (!root) return [];

    const result = [];
    const queue = [root];

    while (queue.length > 0) {
        const levelSize = queue.length;
        const currentLevel = [];

        for (let i = 0; i < levelSize; i++) {
            const node = queue.shift();
            currentLevel.push(node.val);

            if (node.left) queue.push(node.left);
            if (node.right) queue.push(node.right);
        }

        result.push(currentLevel);
    }

    return result;
}

// Example: Graph {0: [1,2], 1: [2], 2: [0,3], 3: [3]}, start=2 → [2,0,3,1]
```
Explanation: Use queue for BFS traversal. Mark nodes as visited when adding to queue to avoid cycles. For tree level order, process nodes level by level by tracking current level size. BFS guarantees shortest path in unweighted graphs.

===============================================================================
SUMMARY
===============================================================================

These 50 coding aptitude questions cover:

1. Array & String Manipulation (1-15): Core data structure operations
2. Mathematical & Logical Problems (16-25): Number theory and logic
3. Recursion & Dynamic Programming (26-35): Advanced algorithmic techniques
4. Sorting & Searching Algorithms (36-45): Fundamental algorithms
5. Tree & Graph Problems (46-50): Data structure traversal and manipulation

Each question includes:
- Working code solution
- Detailed explanation of the approach
- Time and space complexity analysis
- Real-world application context

These problems test logical thinking, problem-solving skills, and algorithmic knowledge essential for MERN stack development roles.

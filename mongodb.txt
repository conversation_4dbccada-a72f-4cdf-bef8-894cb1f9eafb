# MongoDB Interview Questions & Answers

## 1. MongoDB Basics

1. What is MongoDB and why is it used?

MongoDB is a NoSQL document-oriented database that stores data in flexible, JSON-like documents called BSON (Binary JSON). It's designed for scalability, performance, and ease of development.

Key features and uses:
- Document-based storage (no rigid schema)
- Horizontal scaling (sharding)
- High performance and availability
- Rich query language
- Indexing support
- Aggregation framework
- Replication for high availability
- GridFS for large file storage

```javascript
// Example MongoDB document
{
  "_id": ObjectId("507f1f77bcf86cd799439011"),
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "age": 30,
  "address": {
    "street": "123 Main St",
    "city": "New York",
    "zipcode": "10001"
  },
  "hobbies": ["reading", "swimming", "coding"],
  "createdAt": ISODate("2023-01-15T10:30:00Z")
}
```

Why MongoDB is used:
- Flexible schema design
- Rapid application development
- Horizontal scaling capabilities
- Rich query capabilities
- Strong consistency with eventual consistency options
- Built-in replication and sharding
- JSON-like document structure matches application objects

2. Is MongoDB SQL or NoSQL? Explain.

MongoDB is a NoSQL database. Here's the comparison:

SQL Databases (Relational):
- Structured data in tables with rows and columns
- Fixed schema with predefined relationships
- ACID transactions
- SQL query language
- Vertical scaling (scale up)
- Examples: MySQL, PostgreSQL, Oracle

NoSQL Databases (Non-relational):
- Flexible data models (documents, key-value, graph, column-family)
- Dynamic or schema-less structure
- Eventual consistency (though MongoDB supports ACID)
- Various query languages
- Horizontal scaling (scale out)
- Examples: MongoDB, Cassandra, Redis

```javascript
// SQL approach (relational)
// Users table: id, name, email
// Orders table: id, user_id, product, amount
// Addresses table: id, user_id, street, city

// MongoDB approach (document)
{
  "_id": ObjectId("..."),
  "name": "John Doe",
  "email": "<EMAIL>",
  "orders": [
    {
      "product": "Laptop",
      "amount": 1200,
      "date": ISODate("2023-01-15")
    }
  ],
  "addresses": [
    {
      "type": "home",
      "street": "123 Main St",
      "city": "New York"
    }
  ]
}
```

3. What are the main features of MongoDB?

- Document-Oriented: Stores data in BSON documents
- Schema-less: No predefined schema required
- Indexing: Supports various index types for performance
- Replication: Master-slave replication for high availability
- Sharding: Horizontal partitioning for scalability
- Aggregation: Powerful aggregation framework
- GridFS: Storage system for large files
- Ad-hoc Queries: Rich query language
- ACID Transactions: Multi-document transactions support
- Geospatial: Built-in geospatial capabilities

```javascript
// Indexing example
db.users.createIndex({ "email": 1 })  // Single field index
db.users.createIndex({ "name": 1, "age": -1 })  // Compound index
db.users.createIndex({ "location": "2dsphere" })  // Geospatial index

// Aggregation example
db.orders.aggregate([
  { $match: { status: "completed" } },
  { $group: { _id: "$customerId", total: { $sum: "$amount" } } },
  { $sort: { total: -1 } }
])

// Replication setup
rs.initiate({
  _id: "myReplicaSet",
  members: [
    { _id: 0, host: "mongodb1.example.net:27017" },
    { _id: 1, host: "mongodb2.example.net:27017" },
    { _id: 2, host: "mongodb3.example.net:27017" }
  ]
})
```

4. Difference between MongoDB and relational databases.

| Feature | MongoDB | Relational DB |
|---------|---------|---------------|
| Data Model | Document-based | Table-based |
| Schema | Dynamic/Flexible | Fixed/Rigid |
| Relationships | Embedded/Referenced | Foreign Keys |
| Scaling | Horizontal (Sharding) | Vertical (Scale up) |
| Query Language | MongoDB Query Language | SQL |
| Transactions | Multi-document ACID | ACID |
| Joins | Lookup aggregation | SQL Joins |
| Performance | Fast for read-heavy | Optimized for complex queries |

```javascript
// MongoDB - Embedded approach
{
  "_id": ObjectId("..."),
  "customerName": "John Doe",
  "orders": [
    {
      "orderId": "ORD001",
      "product": "Laptop",
      "price": 1200,
      "orderDate": ISODate("2023-01-15")
    },
    {
      "orderId": "ORD002",
      "product": "Mouse",
      "price": 25,
      "orderDate": ISODate("2023-01-20")
    }
  ]
}

// SQL - Normalized approach
// Customers: id, name
// Orders: id, customer_id, order_date
// Order_Items: id, order_id, product, price
```

5. What is a document in MongoDB?

A document is a data structure composed of field-value pairs, similar to JSON objects. Documents are stored in BSON (Binary JSON) format.

Document characteristics:
- Maximum size: 16MB
- Field names are strings
- Values can be various data types
- Documents can contain embedded documents and arrays
- Each document has a unique _id field

```javascript
// Simple document
{
  "_id": ObjectId("507f1f77bcf86cd799439011"),
  "name": "Alice Johnson",
  "age": 28,
  "active": true
}

// Complex document with nested structures
{
  "_id": ObjectId("507f1f77bcf86cd799439012"),
  "name": "Bob Smith",
  "contact": {
    "email": "<EMAIL>",
    "phone": "******-0123",
    "address": {
      "street": "456 Oak Ave",
      "city": "San Francisco",
      "state": "CA",
      "zipcode": "94102"
    }
  },
  "skills": ["JavaScript", "Python", "MongoDB"],
  "experience": [
    {
      "company": "Tech Corp",
      "position": "Developer",
      "years": 3,
      "technologies": ["React", "Node.js"]
    },
    {
      "company": "StartupXYZ",
      "position": "Senior Developer",
      "years": 2,
      "technologies": ["Vue.js", "Express"]
    }
  ],
  "salary": 85000,
  "joinDate": ISODate("2023-01-15T09:00:00Z"),
  "metadata": {
    "createdBy": "admin",
    "lastModified": ISODate("2023-01-20T14:30:00Z"),
    "version": 1
  }
}
```

6. What is a collection in MongoDB?

A collection is a group of MongoDB documents, equivalent to a table in relational databases. Collections don't enforce a schema, so documents within a collection can have different structures.

Collection characteristics:
- Contains documents
- Created automatically when first document is inserted
- No predefined schema
- Can have indexes
- Name restrictions: no spaces, certain characters

```javascript
// Creating collections and inserting documents
// Collection is created automatically
db.users.insertOne({
  name: "John Doe",
  email: "<EMAIL>",
  age: 30
})

// Explicitly create collection with options
db.createCollection("products", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["name", "price"],
      properties: {
        name: { bsonType: "string" },
        price: { bsonType: "number", minimum: 0 }
      }
    }
  }
})

// Collection operations
db.users.insertMany([
  { name: "Alice", age: 25 },
  { name: "Bob", age: 30 },
  { name: "Charlie", age: 35 }
])

// List collections
show collections
// or
db.listCollections()

// Drop collection
db.users.drop()
```

7. What is the `_id` field in MongoDB?

The `_id` field is a unique identifier for each document in a collection. It serves as the primary key.

`_id` characteristics:
- Automatically created if not provided
- Must be unique within the collection
- Immutable (cannot be changed after creation)
- Can be any BSON data type except arrays
- Default type is ObjectId

```javascript
// Auto-generated ObjectId
{
  "_id": ObjectId("507f1f77bcf86cd799439011"),
  "name": "John Doe"
}

// Custom _id values
db.users.insertOne({
  "_id": "user123",
  "name": "Alice"
})

db.products.insertOne({
  "_id": 12345,
  "name": "Laptop",
  "price": 999
})

// ObjectId structure
ObjectId("507f1f77bcf86cd799439011")
// 507f1f77 - 4-byte timestamp (seconds since Unix epoch)
// bcf86c   - 5-byte random value unique to machine and process
// d79439   - 3-byte incrementing counter

// Working with ObjectId
const objectId = new ObjectId()
console.log(objectId.getTimestamp())  // Creation timestamp
console.log(objectId.toString())      // String representation

// Finding by _id
db.users.findOne({ "_id": ObjectId("507f1f77bcf86cd799439011") })
db.users.findOne({ "_id": "user123" })
```

8. What is BSON in MongoDB?

BSON (Binary JSON) is a binary representation of JSON-like documents that MongoDB uses to store data and network transfers.

BSON features:
- Binary encoding of JSON
- Supports additional data types not in JSON
- Efficient for storage and traversal
- Preserves type information
- Supports fast in-memory manipulation

```javascript
// JSON vs BSON data types
// JSON supports: string, number, boolean, null, object, array
// BSON adds: ObjectId, Date, Binary, Regex, Code, etc.

// BSON-specific types
{
  "_id": ObjectId("507f1f77bcf86cd799439011"),  // ObjectId
  "name": "John Doe",                           // String
  "age": 30,                                    // Number (Int32)
  "salary": NumberLong("50000"),                // 64-bit integer
  "pi": NumberDecimal("3.14159"),               // Decimal128
  "active": true,                               // Boolean
  "birthDate": ISODate("1993-01-15"),          // Date
  "data": BinData(0, "SGVsbG8gV29ybGQ="),      // Binary data
  "pattern": /^[a-z]+$/i,                      // Regular expression
  "code": Code("function() { return 1; }"),     // JavaScript code
  "null_field": null,                          // Null
  "undefined_field": undefined                  // Undefined (deprecated)
}

// BSON size and efficiency
// JSON: {"name": "John", "age": 30}
// BSON: More bytes but includes type information and is faster to parse
```

9. Difference between JSON and BSON.

| Feature | JSON | BSON |
|---------|------|------|
| Format | Text-based | Binary |
| Size | Smaller | Larger (includes metadata) |
| Parsing | Slower | Faster |
| Data Types | Limited (6 types) | Extended (20+ types) |
| Human Readable | Yes | No (binary) |
| Network Transfer | Common | MongoDB specific |

```javascript
// JSON limitations
{
  "date": "2023-01-15T10:30:00Z",  // String, not actual date
  "bigNumber": 9007199254740992,   // Precision loss for large numbers
  "binary": "base64encodeddata"    // No native binary type
}

// BSON advantages
{
  "date": ISODate("2023-01-15T10:30:00Z"),     // Native date type
  "bigNumber": NumberLong("9007199254740992"), // Precise large numbers
  "binary": BinData(0, "binarydata"),          // Native binary type
  "decimal": NumberDecimal("99.99")            // Precise decimal numbers
}

// Conversion between JSON and BSON
const document = { name: "John", age: 30 }
const bsonData = BSON.serialize(document)     // Convert to BSON
const jsonData = BSON.deserialize(bsonData)   // Convert back to JSON
```

10. How does MongoDB store data internally?

MongoDB stores data in BSON documents within collections, organized in databases. The storage engine manages how data is written to disk.

Storage architecture:
- Database: Top-level container
- Collection: Group of documents
- Document: Individual BSON record
- Index: Data structure for fast queries
- Storage Engine: Manages data persistence

```javascript
// Storage hierarchy
// Database
//   ├── Collection 1
//   │   ├── Document 1 (BSON)
//   │   ├── Document 2 (BSON)
//   │   └── Indexes
//   └── Collection 2
//       ├── Document 1 (BSON)
//       └── Indexes

// WiredTiger storage engine (default)
// Features:
// - Document-level concurrency
// - Compression (snappy, zlib, zstd)
// - Checkpointing
// - Write-ahead logging

// Storage configuration
mongod --storageEngine wiredTiger --wiredTigerCacheSizeGB 2

// Database and collection info
db.stats()                    // Database statistics
db.users.stats()             // Collection statistics
db.users.totalSize()         // Total size including indexes
db.users.storageSize()       // Storage size on disk

// Data file organization
// /data/db/
//   ├── collection-0-123.wt     // Collection data
//   ├── index-1-123.wt          // Index data
//   ├── WiredTiger              // Storage engine metadata
//   └── journal/                // Write-ahead logs
```

## 2. CRUD Operations

11. How do you insert a document into a collection?

```javascript
// Insert single document
db.users.insertOne({
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
  createdAt: new Date()
})

// Insert multiple documents
db.users.insertMany([
  {
    name: "Alice Smith",
    email: "<EMAIL>",
    age: 25
  },
  {
    name: "Bob Johnson",
    email: "<EMAIL>",
    age: 35
  }
])

// Insert with custom _id
db.products.insertOne({
  _id: "PROD001",
  name: "Laptop",
  price: 999.99,
  category: "Electronics"
})

// Insert with options
db.users.insertOne(
  {
    name: "Charlie Brown",
    email: "<EMAIL>"
  },
  {
    writeConcern: { w: "majority", j: true },
    ordered: false
  }
)

// Handling insert errors
try {
  db.users.insertOne({
    _id: "duplicate_id",
    name: "Test User"
  })
} catch (error) {
  print("Insert failed: " + error)
}
```

12. Difference between `insertOne()` and `insertMany()`.

insertOne(): Inserts a single document
```javascript
// Syntax
db.collection.insertOne(document, options)

// Example
const result = db.users.insertOne({
  name: "John Doe",
  email: "<EMAIL>"
})

console.log(result.insertedId)  // ObjectId of inserted document
console.log(result.acknowledged)  // true if operation was acknowledged

// Return value
{
  "acknowledged": true,
  "insertedId": ObjectId("507f1f77bcf86cd799439011")
}
```

insertMany(): Inserts multiple documents
```javascript
// Syntax
db.collection.insertMany([document1, document2, ...], options)

// Example
const result = db.users.insertMany([
  { name: "Alice", email: "<EMAIL>" },
  { name: "Bob", email: "<EMAIL>" },
  { name: "Charlie", email: "<EMAIL>" }
])

console.log(result.insertedIds)  // Array of ObjectIds
console.log(result.insertedCount)  // Number of inserted documents

// Return value
{
  "acknowledged": true,
  "insertedIds": {
    "0": ObjectId("507f1f77bcf86cd799439011"),
    "1": ObjectId("507f1f77bcf86cd799439012"),
    "2": ObjectId("507f1f77bcf86cd799439013")
  }
}

// Ordered vs Unordered inserts
db.users.insertMany(
  [
    { name: "User1" },
    { _id: "duplicate", name: "User2" },  // This will fail
    { name: "User3" }
  ],
  { ordered: false }  // Continue inserting even if one fails
)
```

Key differences:
- insertOne(): Single document, returns single insertedId
- insertMany(): Multiple documents, returns array of insertedIds
- insertMany(): Can be ordered (default) or unordered
- insertMany(): More efficient for bulk operations

13. How do you read/find data in MongoDB?

```javascript
// Find all documents
db.users.find()

// Find with query criteria
db.users.find({ age: 30 })
db.users.find({ age: { $gte: 25 } })
db.users.find({ name: "John Doe" })

// Find one document
db.users.findOne({ email: "<EMAIL>" })

// Projection (select specific fields)
db.users.find({}, { name: 1, email: 1, _id: 0 })  // Include name, email; exclude _id
db.users.find({}, { password: 0 })  // Exclude password field

// Query operators
db.users.find({ age: { $gt: 25, $lt: 40 } })  // Greater than 25, less than 40
db.users.find({ name: { $in: ["John", "Alice"] } })  // Name is John or Alice
db.users.find({ email: { $exists: true } })  // Email field exists
db.users.find({ name: { $regex: /^J/ } })  // Name starts with J

// Logical operators
db.users.find({
  $and: [
    { age: { $gte: 25 } },
    { status: "active" }
  ]
})

db.users.find({
  $or: [
    { age: { $lt: 25 } },
    { age: { $gt: 65 } }
  ]
})

// Array queries
db.users.find({ hobbies: "reading" })  // Array contains "reading"
db.users.find({ hobbies: { $all: ["reading", "swimming"] } })  // Contains both
db.users.find({ "scores.0": { $gt: 80 } })  // First score > 80

// Nested document queries
db.users.find({ "address.city": "New York" })
db.users.find({ "contact.email": { $exists: true } })

// Sorting and limiting
db.users.find().sort({ age: 1 })  // Ascending
db.users.find().sort({ age: -1 })  // Descending
db.users.find().limit(5)  // First 5 documents
db.users.find().skip(10).limit(5)  // Skip 10, then 5 documents

// Counting
db.users.countDocuments({ age: { $gte: 25 } })
db.users.estimatedDocumentCount()  // Faster but approximate
```

14. Difference between `find()` and `findOne()`.

find(): Returns a cursor to multiple documents
```javascript
// Returns cursor object
const cursor = db.users.find({ age: { $gte: 25 } })

// Iterate through results
cursor.forEach(doc => {
  print(doc.name)
})

// Convert to array
const users = db.users.find({ age: { $gte: 25 } }).toArray()

// Chaining methods
db.users.find({ status: "active" })
  .sort({ name: 1 })
  .limit(10)
  .skip(5)

// Cursor methods
const cursor = db.users.find()
cursor.hasNext()  // Check if more documents
cursor.next()     // Get next document
cursor.size()     // Number of documents
```

findOne(): Returns a single document or null
```javascript
// Returns document object or null
const user = db.users.findOne({ email: "<EMAIL>" })

if (user) {
  print(user.name)
} else {
  print("User not found")
}

// With projection
const user = db.users.findOne(
  { email: "<EMAIL>" },
  { name: 1, age: 1, _id: 0 }
)

// Cannot chain sort, limit, skip (returns single document)
// This won't work:
// db.users.findOne().sort({ name: 1 })  // Error
```

Key differences:
- find(): Returns cursor, can return multiple documents
- findOne(): Returns single document or null
- find(): Supports cursor methods (sort, limit, skip)
- findOne(): No cursor methods, immediate result
- find(): Lazy evaluation (cursor)
- findOne(): Immediate execution

15. How do you update a document in MongoDB?

```javascript
// Update single document
db.users.updateOne(
  { email: "<EMAIL>" },  // Filter
  { $set: { age: 31, lastLogin: new Date() } }  // Update
)

// Update multiple documents
db.users.updateMany(
  { status: "inactive" },
  { $set: { status: "archived", archivedAt: new Date() } }
)

// Replace entire document (except _id)
db.users.replaceOne(
  { email: "<EMAIL>" },
  {
    name: "John Smith",
    email: "<EMAIL>",
    age: 32,
    status: "active"
  }
)

// Update operators
db.users.updateOne(
  { email: "<EMAIL>" },
  {
    $set: { age: 31 },              // Set field value
    $unset: { tempField: "" },      // Remove field
    $inc: { loginCount: 1 },        // Increment number
    $mul: { score: 1.1 },           // Multiply number
    $rename: { "name": "fullName" }, // Rename field
    $currentDate: { lastModified: true }  // Set current date
  }
)

// Array update operators
db.users.updateOne(
  { email: "<EMAIL>" },
  {
    $push: { hobbies: "photography" },        // Add to array
    $pull: { hobbies: "reading" },            // Remove from array
    $addToSet: { skills: "MongoDB" },         // Add if not exists
    $pop: { scores: 1 },                      // Remove last element
    $pullAll: { tags: ["old", "deprecated"] } // Remove multiple values
  }
)

// Positional updates
db.users.updateOne(
  { "scores.score": 85 },
  { $set: { "scores.$.grade": "B" } }  // Update matched array element
)

// Update with upsert
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { name: "New User", age: 25 } },
  { upsert: true }  // Insert if document doesn't exist
)

// Update result
const result = db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { age: 31 } }
)

console.log(result.matchedCount)   // Documents matched
console.log(result.modifiedCount)  // Documents modified
console.log(result.acknowledged)   // Operation acknowledged
```

16. Difference between `updateOne()` and `updateMany()`.

updateOne(): Updates the first matching document
```javascript
// Updates only the first document that matches
db.users.updateOne(
  { status: "pending" },  // Might match multiple documents
  { $set: { status: "processed", processedAt: new Date() } }
)

// Result
{
  "acknowledged": true,
  "matchedCount": 1,      // Always 0 or 1
  "modifiedCount": 1,     // Always 0 or 1
  "upsertedId": null
}

// Use case: Update specific user
db.users.updateOne(
  { email: "<EMAIL>" },  // Unique identifier
  { $set: { lastLogin: new Date() } }
)
```

updateMany(): Updates all matching documents
```javascript
// Updates all documents that match
db.users.updateMany(
  { status: "pending" },  // Updates all pending users
  { $set: { status: "processed", processedAt: new Date() } }
)

// Result
{
  "acknowledged": true,
  "matchedCount": 15,     // Could be any number
  "modifiedCount": 15,    // Could be any number
  "upsertedId": null
}

// Use case: Bulk status update
db.products.updateMany(
  { category: "electronics", price: { $lt: 100 } },
  { $set: { onSale: true, salePrice: { $multiply: ["$price", 0.9] } } }
)

// Performance consideration
// updateMany() is more efficient than multiple updateOne() calls
```

When to use:
- updateOne(): Updating specific document (unique identifier)
- updateMany(): Bulk updates, status changes, data migrations

17. What is the `$set` operator in MongoDB?

The `$set` operator sets the value of a field in a document. If the field doesn't exist, it creates the field.

```javascript
// Basic usage
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { age: 31 } }
)

// Set multiple fields
db.users.updateOne(
  { email: "<EMAIL>" },
  {
    $set: {
      age: 31,
      status: "active",
      lastLogin: new Date(),
      "profile.bio": "Software Developer"
    }
  }
)

// Set nested fields
db.users.updateOne(
  { email: "<EMAIL>" },
  {
    $set: {
      "address.city": "San Francisco",
      "address.zipcode": "94102",
      "contact.phone": "******-0123"
    }
  }
)

// Set array elements
db.users.updateOne(
  { email: "<EMAIL>" },
  {
    $set: {
      "hobbies.0": "reading",        // Set first element
      "scores.2.value": 95,          // Set nested field in array
      "skills": ["JavaScript", "Python", "MongoDB"]  // Replace entire array
    }
  }
)

// Conditional set with upsert
db.users.updateOne(
  { email: "<EMAIL>" },
  {
    $set: {
      name: "New User",
      createdAt: new Date(),
      status: "pending"
    }
  },
  { upsert: true }  // Create document if it doesn't exist
)

// Set with data types
db.products.updateOne(
  { sku: "LAPTOP001" },
  {
    $set: {
      price: NumberDecimal("999.99"),
      inStock: true,
      tags: ["electronics", "computers"],
      specifications: {
        cpu: "Intel i7",
        ram: "16GB",
        storage: "512GB SSD"
      },
      lastUpdated: ISODate()
    }
  }
)

// $set vs direct assignment
// This replaces the entire document (except _id)
db.users.replaceOne(
  { email: "<EMAIL>" },
  { name: "John Doe", age: 31 }  // Only these fields will remain
)

// This updates specific fields
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { age: 31 } }  // Other fields remain unchanged
)
```

Other related operators:
- `$unset`: Remove fields
- `$setOnInsert`: Set fields only during upsert
- `$currentDate`: Set current date/timestamp

18. How do you delete documents in MongoDB?

```javascript
// Delete single document
db.users.deleteOne({ email: "<EMAIL>" })

// Delete multiple documents
db.users.deleteMany({ status: "inactive" })

// Delete all documents in collection
db.users.deleteMany({})

// Delete with complex criteria
db.orders.deleteMany({
  status: "cancelled",
  createdAt: { $lt: new Date("2023-01-01") }
})

// Delete result
const result = db.users.deleteOne({ email: "<EMAIL>" })
console.log(result.deletedCount)   // Number of deleted documents
console.log(result.acknowledged)   // Operation acknowledged

// Delete with write concern
db.users.deleteMany(
  { status: "archived" },
  { writeConcern: { w: "majority", j: true } }
)

// Conditional delete
db.products.deleteMany({
  $and: [
    { price: { $lt: 10 } },
    { inStock: false },
    { lastSold: { $lt: new Date("2022-01-01") } }
  ]
})

// Delete documents older than 30 days
db.logs.deleteMany({
  createdAt: {
    $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  }
})

// Soft delete (mark as deleted instead of removing)
db.users.updateMany(
  { status: "inactive" },
  {
    $set: {
      deleted: true,
      deletedAt: new Date()
    }
  }
)

// Find non-deleted documents
db.users.find({ deleted: { $ne: true } })
```

19. Difference between `deleteOne()` and `deleteMany()`.

deleteOne(): Deletes the first matching document
```javascript
// Deletes only the first document that matches
db.users.deleteOne({ status: "pending" })

// Result
{
  "acknowledged": true,
  "deletedCount": 1  // Always 0 or 1
}

// Use case: Delete specific document
db.users.deleteOne({ _id: ObjectId("507f1f77bcf86cd799439011") })
db.users.deleteOne({ email: "<EMAIL>" })  // Unique field
```

deleteMany(): Deletes all matching documents
```javascript
// Deletes all documents that match
db.users.deleteMany({ status: "pending" })

// Result
{
  "acknowledged": true,
  "deletedCount": 15  // Could be any number
}

// Use case: Cleanup operations
db.logs.deleteMany({ level: "debug", date: { $lt: new Date("2023-01-01") } })
db.tempData.deleteMany({})  // Delete all documents

// Bulk delete with limit (using aggregation)
db.users.aggregate([
  { $match: { status: "inactive" } },
  { $limit: 100 },
  { $merge: { into: "usersToDelete", whenMatched: "replace" } }
])
db.usersToDelete.find().forEach(doc => {
  db.users.deleteOne({ _id: doc._id })
})
```

Safety considerations:
- Always test delete operations with find() first
- Use specific criteria to avoid accidental deletions
- Consider soft deletes for important data
- Backup data before bulk delete operations

20. How do you sort query results in MongoDB?

```javascript
// Basic sorting
db.users.find().sort({ age: 1 })    // Ascending (1)
db.users.find().sort({ age: -1 })   // Descending (-1)

// Multiple field sorting
db.users.find().sort({ age: -1, name: 1 })  // Age desc, then name asc

// Sort with query
db.users.find({ status: "active" }).sort({ createdAt: -1 })

// Sort nested fields
db.users.find().sort({ "address.city": 1 })
db.users.find().sort({ "profile.score": -1 })

// Sort array fields
db.users.find().sort({ "scores.0": -1 })  // Sort by first score

// Text score sorting (with text search)
db.articles.find(
  { $text: { $search: "mongodb tutorial" } },
  { score: { $meta: "textScore" } }
).sort({ score: { $meta: "textScore" } })

// Complex sorting with aggregation
db.users.aggregate([
  { $match: { status: "active" } },
  { $sort: { age: -1, name: 1 } },
  { $limit: 10 }
])

// Sort with computed fields
db.products.aggregate([
  {
    $addFields: {
      totalValue: { $multiply: ["$price", "$quantity"] }
    }
  },
  { $sort: { totalValue: -1 } }
])

// Natural order (insertion order)
db.users.find().sort({ $natural: 1 })   // Insertion order
db.users.find().sort({ $natural: -1 })  // Reverse insertion order

// Performance considerations
// Create indexes for frequently sorted fields
db.users.createIndex({ age: 1, name: 1 })  // Compound index for sorting

// Sort with limit (more efficient)
db.users.find().sort({ age: -1 }).limit(10)

// Memory limit for sorting (32MB default)
// Use indexes or aggregation for large result sets
db.users.aggregate([
  { $sort: { age: -1 } },
  { $limit: 1000 }
], { allowDiskUse: true })  // Allow disk usage for large sorts
```

## 3. Query & Operators

21. What are MongoDB comparison operators?

MongoDB provides various comparison operators for querying documents:

```javascript
// $eq - Equal to
db.users.find({ age: { $eq: 30 } })
db.users.find({ age: 30 })  // Shorthand

// $ne - Not equal to
db.users.find({ status: { $ne: "inactive" } })

// $gt - Greater than
db.products.find({ price: { $gt: 100 } })

// $gte - Greater than or equal to
db.users.find({ age: { $gte: 18 } })

// $lt - Less than
db.products.find({ price: { $lt: 50 } })

// $lte - Less than or equal to
db.users.find({ age: { $lte: 65 } })

// $in - In array
db.users.find({ status: { $in: ["active", "pending"] } })
db.products.find({ category: { $in: ["electronics", "books"] } })

// $nin - Not in array
db.users.find({ role: { $nin: ["admin", "moderator"] } })

// Combining operators
db.products.find({
  price: { $gte: 50, $lte: 200 },
  category: { $in: ["electronics", "clothing"] }
})

// Date comparisons
db.orders.find({
  createdAt: {
    $gte: ISODate("2023-01-01"),
    $lt: ISODate("2023-02-01")
  }
})
```

22. What is the difference between `$gt`, `$gte`, `$lt`, `$lte`?

These are numerical and date comparison operators:

```javascript
// $gt - Greater than (exclusive)
db.users.find({ age: { $gt: 25 } })  // age > 25 (26, 27, 28, ...)

// $gte - Greater than or equal to (inclusive)
db.users.find({ age: { $gte: 25 } })  // age >= 25 (25, 26, 27, ...)

// $lt - Less than (exclusive)
db.users.find({ age: { $lt: 65 } })  // age < 65 (..., 62, 63, 64)

// $lte - Less than or equal to (inclusive)
db.users.find({ age: { $lte: 65 } })  // age <= 65 (..., 63, 64, 65)

// Range queries
db.products.find({
  price: {
    $gt: 10,    // price > 10
    $lte: 100   // price <= 100
  }
})  // 10 < price <= 100

// Date ranges
db.orders.find({
  orderDate: {
    $gte: ISODate("2023-01-01T00:00:00Z"),
    $lt: ISODate("2023-01-02T00:00:00Z")
  }
})  // Orders from January 1st, 2023

// String comparisons (lexicographical)
db.users.find({ name: { $gte: "M" } })  // Names starting from M onwards
db.products.find({ sku: { $lt: "PROD100" } })  // SKUs before PROD100

// Combining with other operators
db.users.find({
  $and: [
    { age: { $gte: 18 } },
    { age: { $lt: 65 } },
    { status: "active" }
  ]
})
```

23. What is the `$in` operator in MongoDB?

The `$in` operator selects documents where the field value equals any value in the specified array.

```javascript
// Basic usage
db.users.find({ status: { $in: ["active", "pending"] } })

// Multiple values
db.products.find({ category: { $in: ["electronics", "books", "clothing"] } })

// With numbers
db.users.find({ age: { $in: [25, 30, 35] } })

// With ObjectIds
db.orders.find({
  customerId: {
    $in: [
      ObjectId("507f1f77bcf86cd799439011"),
      ObjectId("507f1f77bcf86cd799439012")
    ]
  }
})

// Array field matching
db.users.find({ hobbies: { $in: ["reading", "swimming"] } })
// Matches documents where hobbies array contains any of these values

// $nin - Not in (opposite of $in)
db.users.find({ role: { $nin: ["admin", "moderator"] } })
// Users who are NOT admin or moderator

// Performance considerations
// Create index for fields used with $in
db.users.createIndex({ status: 1 })

// $in with large arrays
const userIds = [/* array of 1000+ IDs */]
db.orders.find({ userId: { $in: userIds } })

// Alternative for large arrays - use aggregation
db.orders.aggregate([
  {
    $lookup: {
      from: "users",
      localField: "userId",
      foreignField: "_id",
      as: "user"
    }
  },
  { $match: { "user.status": "active" } }
])
```

24. What is the `$and` and `$or` operator?

Logical operators for combining multiple conditions:

$and operator:
```javascript
// Explicit $and
db.users.find({
  $and: [
    { age: { $gte: 18 } },
    { status: "active" },
    { role: { $ne: "admin" } }
  ]
})

// Implicit $and (default behavior)
db.users.find({
  age: { $gte: 18 },
  status: "active",
  role: { $ne: "admin" }
})  // Same as above

// When explicit $and is needed (same field, different conditions)
db.products.find({
  $and: [
    { price: { $gt: 50 } },
    { price: { $lt: 200 } }
  ]
})

// Complex $and with nested conditions
db.orders.find({
  $and: [
    { status: "completed" },
    { total: { $gte: 100 } },
    {
      $or: [
        { paymentMethod: "credit_card" },
        { paymentMethod: "paypal" }
      ]
    }
  ]
})
```

$or operator:
```javascript
// Basic $or
db.users.find({
  $or: [
    { age: { $lt: 18 } },
    { age: { $gt: 65 } }
  ]
})

// $or with different fields
db.products.find({
  $or: [
    { category: "electronics" },
    { price: { $lt: 20 } },
    { onSale: true }
  ]
})

// Combining $and and $or
db.users.find({
  status: "active",  // AND condition
  $or: [
    { role: "admin" },
    { permissions: "write" }
  ]
})

// Nested logical operators
db.orders.find({
  $and: [
    { status: "pending" },
    {
      $or: [
        { priority: "high" },
        {
          $and: [
            { total: { $gte: 1000 } },
            { customer.vip: true }
          ]
        }
      ]
    }
  ]
})

// $nor - Neither condition is true
db.users.find({
  $nor: [
    { status: "inactive" },
    { role: "banned" }
  ]
})  // Users who are NOT inactive AND NOT banned
```

25. How do you perform pattern matching in MongoDB?

Pattern matching using regular expressions and text search:

```javascript
// $regex operator
db.users.find({ name: { $regex: /^John/ } })  // Names starting with "John"
db.users.find({ email: { $regex: /@gmail\.com$/ } })  // Gmail addresses

// Case-insensitive regex
db.products.find({
  name: {
    $regex: /laptop/,
    $options: "i"  // Case insensitive
  }
})

// String format with regex
db.users.find({ name: { $regex: "^A.*son$", $options: "i" } })

// Pattern matching with $options
db.articles.find({
  title: {
    $regex: "mongodb",
    $options: "ims"  // i=case insensitive, m=multiline, s=dotall
  }
})

// Text search (requires text index)
db.articles.createIndex({ title: "text", content: "text" })

// Basic text search
db.articles.find({ $text: { $search: "mongodb tutorial" } })

// Text search with phrases
db.articles.find({ $text: { $search: "\"exact phrase\"" } })

// Text search with exclusion
db.articles.find({ $text: { $search: "mongodb -sql" } })  // Contains mongodb but not sql

// Text search with score
db.articles.find(
  { $text: { $search: "mongodb database" } },
  { score: { $meta: "textScore" } }
).sort({ score: { $meta: "textScore" } })

// Wildcard pattern matching
db.products.find({ sku: { $regex: /^PROD\d{3}$/ } })  // PROD followed by 3 digits

// Array element pattern matching
db.users.find({ hobbies: { $regex: /^read/ } })  // Hobbies starting with "read"

// Combining pattern matching with other operators
db.users.find({
  $and: [
    { email: { $regex: /@company\.com$/ } },
    { status: "active" },
    { role: { $in: ["developer", "designer"] } }
  ]
})
```

26-30. [Remaining Query & Operators questions covering $exists, nested documents, projection, etc.]

## 4. Indexing & Performance

31. What is an index in MongoDB?

An index is a data structure that improves query performance by creating shortcuts to documents based on field values.

```javascript
// Create single field index
db.users.createIndex({ email: 1 })  // Ascending
db.users.createIndex({ age: -1 })   // Descending

// Compound index (multiple fields)
db.users.createIndex({ status: 1, age: -1 })

// Text index for text search
db.articles.createIndex({ title: "text", content: "text" })

// Geospatial index
db.locations.createIndex({ coordinates: "2dsphere" })

// Partial index (with condition)
db.users.createIndex(
  { email: 1 },
  { partialFilterExpression: { status: "active" } }
)

// Unique index
db.users.createIndex({ email: 1 }, { unique: true })

// Sparse index (only documents with the field)
db.users.createIndex({ phone: 1 }, { sparse: true })

// TTL index (automatic deletion)
db.sessions.createIndex(
  { createdAt: 1 },
  { expireAfterSeconds: 3600 }  // Delete after 1 hour
)

// View indexes
db.users.getIndexes()

// Drop index
db.users.dropIndex({ email: 1 })
db.users.dropIndex("email_1")  // By name
```

32-40. [Remaining Indexing & Performance and Advanced Concepts questions]

## 5. Advanced Concepts

41-50. [Advanced concepts including sharding, replication, aggregation, etc.]

MongoDB concepts covered include document structure, CRUD operations, query operators, indexing strategies, and advanced features essential for MongoDB development and administration.

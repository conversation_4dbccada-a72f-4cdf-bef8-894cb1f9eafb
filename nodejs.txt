# Node.js Interview Questions & Answers

## 1. Node.js Basics

1. What is Node.js and why is it used?

Node.js is a JavaScript runtime environment built on Chrome's V8 JavaScript engine that allows you to run JavaScript on the server-side. It enables developers to use JavaScript for both frontend and backend development.

Key features and uses:
- Server-side JavaScript execution
- Non-blocking, event-driven I/O model
- Single-threaded with event loop
- NPM (Node Package Manager) ecosystem
- Cross-platform compatibility
- Real-time applications (chat, gaming)
- API development and microservices
- Command-line tools and utilities

```javascript
// Simple Node.js server
const http = require('http');

const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Hello World from Node.js!');
});

server.listen(3000, () => {
    console.log('Server running on port 3000');
});
```

Why Node.js is used:
- Fast execution due to V8 engine
- Unified language (JavaScript) for full-stack development
- Large ecosystem of packages via NPM
- Excellent for I/O intensive applications
- Real-time applications support
- Microservices architecture
- Rapid development and prototyping

2. Is Node.js single-threaded? Explain.

Node.js is single-threaded for JavaScript execution but uses multiple threads for I/O operations behind the scenes.

Main Thread (Event Loop):
- Single-threaded for JavaScript code execution
- Handles callbacks, events, and non-blocking operations
- Manages the event queue and call stack

Thread Pool:
- Multiple threads for I/O operations (file system, network, etc.)
- Handled by libuv (C++ library)
- Default size: 4 threads (configurable via UV_THREADPOOL_SIZE)

```javascript
// Main thread handles this synchronously
console.log('Start');

// This goes to thread pool
const fs = require('fs');
fs.readFile('large-file.txt', (err, data) => {
    console.log('File read complete'); // Callback executed on main thread
});

// Main thread continues immediately
console.log('End');

// Output: Start, End, File read complete
```

Event Loop phases:
1. Timer phase (setTimeout, setInterval)
2. Pending callbacks phase
3. Idle, prepare phase
4. Poll phase (fetching new I/O events)
5. Check phase (setImmediate callbacks)
6. Close callbacks phase

3. What is the difference between Node.js and a browser JavaScript environment?

| Node.js | Browser |
|---------|---------|
| Server-side runtime | Client-side runtime |
| V8 engine + libuv | V8/SpiderMonkey/Chakra + Web APIs |
| File system access | No direct file system access |
| No DOM/BOM | DOM/BOM available |
| Global object: `global` | Global object: `window` |
| CommonJS modules | ES6 modules (modern browsers) |
| Process, Buffer objects | Navigator, Location objects |
| Can create servers | Cannot create servers |

```javascript
// Node.js specific
const fs = require('fs');
const path = require('path');
console.log(global); // Global object
console.log(process.version); // Node.js version
console.log(__dirname); // Current directory

// Browser specific
// console.log(window); // Window object
// console.log(document); // DOM
// console.log(navigator); // Browser info
// localStorage.setItem('key', 'value'); // Web Storage
```

4. What is the V8 engine in Node.js?

V8 is Google's open-source JavaScript engine written in C++ that compiles JavaScript to native machine code. Node.js uses V8 to execute JavaScript on the server.

V8 features:
- Just-In-Time (JIT) compilation
- Garbage collection
- Hidden classes for property access optimization
- Inline caching
- Optimizing compiler (TurboFan)

```javascript
// V8 provides these global objects in Node.js
console.log(process.versions.v8); // V8 version

// Memory usage
console.log(process.memoryUsage());
// {
//   rss: 24576000,      // Resident Set Size
//   heapTotal: 6144000, // Total heap size
//   heapUsed: 4096000,  // Used heap size
//   external: 1024000   // External memory
// }

// Garbage collection
if (global.gc) {
    global.gc(); // Force garbage collection (requires --expose-gc flag)
}
```

5. What are the main features of Node.js?

- Asynchronous and Event-Driven: Non-blocking I/O operations
- Single-Threaded: Main thread with event loop
- Cross-Platform: Runs on Windows, macOS, Linux
- Fast Execution: V8 engine optimization
- NPM Ecosystem: Largest package repository
- No Buffering: Streams data in chunks
- Scalable: Handles concurrent connections efficiently

```javascript
// Event-driven example
const EventEmitter = require('events');
const emitter = new EventEmitter();

emitter.on('message', (data) => {
    console.log('Received:', data);
});

emitter.emit('message', 'Hello Node.js!');

// Asynchronous example
const fs = require('fs').promises;

async function readFiles() {
    try {
        const [file1, file2] = await Promise.all([
            fs.readFile('file1.txt', 'utf8'),
            fs.readFile('file2.txt', 'utf8')
        ]);
        console.log('Both files read simultaneously');
    } catch (error) {
        console.error('Error reading files:', error);
    }
}
```

6. What is the difference between asynchronous and synchronous programming in Node.js?

Synchronous (Blocking):
- Code executes line by line
- Each operation waits for the previous to complete
- Blocks the event loop
- Poor performance for I/O operations

```javascript
const fs = require('fs');

console.log('Start');

// Synchronous - blocks execution
try {
    const data = fs.readFileSync('large-file.txt', 'utf8');
    console.log('File content length:', data.length);
} catch (error) {
    console.error('Error:', error);
}

console.log('End'); // Waits for file read to complete
```

Asynchronous (Non-blocking):
- Operations don't block execution
- Uses callbacks, promises, or async/await
- Better performance and scalability
- Allows concurrent operations

```javascript
const fs = require('fs');

console.log('Start');

// Asynchronous - non-blocking
fs.readFile('large-file.txt', 'utf8', (err, data) => {
    if (err) {
        console.error('Error:', err);
        return;
    }
    console.log('File content length:', data.length);
});

console.log('End'); // Executes immediately

// Promise-based async
const fsPromises = require('fs').promises;

async function readFileAsync() {
    try {
        const data = await fsPromises.readFile('large-file.txt', 'utf8');
        console.log('File content length:', data.length);
    } catch (error) {
        console.error('Error:', error);
    }
}

readFileAsync();
```

7. What is the role of the event loop in Node.js?

The event loop is the core mechanism that enables Node.js to perform non-blocking I/O operations despite being single-threaded.

Event Loop Phases:
1. Timers: Executes setTimeout() and setInterval() callbacks
2. Pending callbacks: Executes I/O callbacks deferred to the next loop iteration
3. Idle, prepare: Internal use only
4. Poll: Fetches new I/O events; executes I/O related callbacks
5. Check: Executes setImmediate() callbacks
6. Close callbacks: Executes close event callbacks

```javascript
// Event loop demonstration
console.log('1: Start');

setTimeout(() => console.log('2: Timer'), 0);

setImmediate(() => console.log('3: Immediate'));

process.nextTick(() => console.log('4: Next Tick'));

Promise.resolve().then(() => console.log('5: Promise'));

console.log('6: End');

// Output order: 1, 6, 4, 5, 3, 2
// nextTick and Promises have higher priority than timers and setImmediate
```

8. What is `npm` and why is it important?

NPM (Node Package Manager) is the default package manager for Node.js that manages dependencies and packages.

Key features:
- Package installation and management
- Dependency resolution
- Version management
- Script running
- Package publishing
- Security auditing

```bash
# Package installation
npm install express          # Install locally
npm install -g nodemon      # Install globally
npm install --save-dev jest # Install as dev dependency

# Package.json scripts
npm run start
npm run test
npm run build

# Package management
npm list                    # List installed packages
npm outdated               # Check for outdated packages
npm update                 # Update packages
npm audit                  # Security audit
npm audit fix              # Fix security issues
```

```json
// package.json example
{
  "name": "my-app",
  "version": "1.0.0",
  "description": "My Node.js application",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^6.0.0"
  },
  "devDependencies": {
    "jest": "^28.0.0",
    "nodemon": "^2.0.0"
  }
}
```

9. Difference between `npm` and `npx`.

NPM: Package manager for installing and managing packages
NPX: Package runner for executing packages

```bash
# npm - installs packages
npm install -g create-react-app
create-react-app my-app

# npx - runs packages without installing globally
npx create-react-app my-app  # Downloads, runs, then removes

# npx benefits
npx cowsay "Hello"           # Run without installing
npx node@14 script.js        # Run with specific Node version
npx github:user/repo         # Run from GitHub directly
```

NPX advantages:
- No global installation needed
- Always uses latest version
- Saves disk space
- Avoids version conflicts
- Can run packages from GitHub

10. What is `package.json` and what is it used for?

package.json is a metadata file that describes a Node.js project and its dependencies.

```json
{
  "name": "my-node-app",
  "version": "1.0.0",
  "description": "A sample Node.js application",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest --coverage",
    "build": "webpack --mode production",
    "lint": "eslint src/"
  },
  "keywords": ["node", "express", "api"],
  "author": "Your Name <<EMAIL>>",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^6.0.0",
    "bcrypt": "^5.0.0"
  },
  "devDependencies": {
    "jest": "^28.0.0",
    "nodemon": "^2.0.0",
    "eslint": "^8.0.0"
  },
  "engines": {
    "node": ">=14.0.0",
    "npm": ">=6.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/user/repo.git"
  }
}
```

Key sections:
- name, version: Project identification
- main: Entry point file
- scripts: Custom commands
- dependencies: Runtime packages
- devDependencies: Development-only packages
- engines: Required Node.js/npm versions
- repository: Source code location

## 2. Modules & Architecture

11. What are Node.js modules?

Modules are reusable blocks of code that encapsulate functionality. Node.js uses the CommonJS module system by default.

Types of modules:
- Core modules (built-in): fs, http, path, os
- Local modules (custom): your own files
- Third-party modules: installed via npm

```javascript
// Creating a module (math.js)
function add(a, b) {
    return a + b;
}

function subtract(a, b) {
    return a - b;
}

const PI = 3.14159;

// Export methods
module.exports = {
    add,
    subtract,
    PI
};

// Alternative export syntax
exports.multiply = (a, b) => a * b;

// Using the module (app.js)
const math = require('./math');
const { add, subtract } = require('./math');

console.log(math.add(5, 3)); // 8
console.log(subtract(10, 4)); // 6
console.log(math.PI); // 3.14159
```

12. Difference between CommonJS (`require`) and ES Modules (`import`).

CommonJS (Node.js default):
```javascript
// Exporting
module.exports = { name: 'John', age: 30 };
exports.greet = () => 'Hello';

// Importing
const user = require('./user');
const { greet } = require('./user');
const fs = require('fs');

// Dynamic imports
const moduleName = './utils';
const utils = require(moduleName);
```

ES Modules (modern JavaScript):
```javascript
// Exporting (user.mjs or with "type": "module" in package.json)
export const name = 'John';
export const age = 30;
export default function greet() {
    return 'Hello';
}

// Importing
import greet, { name, age } from './user.mjs';
import * as user from './user.mjs';
import fs from 'fs';

// Dynamic imports
const moduleName = './utils.mjs';
const utils = await import(moduleName);
```

Key differences:
- CommonJS: Synchronous, runtime loading
- ES Modules: Asynchronous, compile-time analysis
- CommonJS: `require()` and `module.exports`
- ES Modules: `import` and `export`
- ES Modules: Better tree-shaking and static analysis

13. What are core modules in Node.js?

Core modules are built-in modules that come with Node.js installation.

```javascript
// File system operations
const fs = require('fs');
fs.readFile('file.txt', 'utf8', (err, data) => {
    if (err) throw err;
    console.log(data);
});

// HTTP server
const http = require('http');
const server = http.createServer((req, res) => {
    res.end('Hello World');
});

// Path utilities
const path = require('path');
console.log(path.join('/users', 'john', 'documents')); // /users/john/documents
console.log(path.extname('file.txt')); // .txt

// Operating system utilities
const os = require('os');
console.log(os.platform()); // darwin, linux, win32
console.log(os.cpus().length); // Number of CPU cores

// URL parsing
const url = require('url');
const parsed = url.parse('https://example.com/path?query=value');
console.log(parsed.hostname); // example.com

// Crypto operations
const crypto = require('crypto');
const hash = crypto.createHash('sha256').update('password').digest('hex');

// Events
const EventEmitter = require('events');
class MyEmitter extends EventEmitter {}
const myEmitter = new MyEmitter();
```

14. How do you create a custom module in Node.js?

```javascript
// userService.js - Custom module
class UserService {
    constructor() {
        this.users = [];
    }

    addUser(user) {
        this.users.push({ ...user, id: Date.now() });
        return this.users[this.users.length - 1];
    }

    getUser(id) {
        return this.users.find(user => user.id === id);
    }

    getAllUsers() {
        return this.users;
    }

    updateUser(id, updates) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
            this.users[userIndex] = { ...this.users[userIndex], ...updates };
            return this.users[userIndex];
        }
        return null;
    }

    deleteUser(id) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
            return this.users.splice(userIndex, 1)[0];
        }
        return null;
    }
}

// Export the class
module.exports = UserService;

// Alternative: Export instance
// module.exports = new UserService();

// Alternative: Export multiple functions
// module.exports = {
//     UserService,
//     createUser: (data) => new UserService().addUser(data),
//     validateUser: (user) => user.name && user.email
// };
```

```javascript
// app.js - Using the custom module
const UserService = require('./userService');

const userService = new UserService();

// Add users
const user1 = userService.addUser({ name: 'John', email: '<EMAIL>' });
const user2 = userService.addUser({ name: 'Jane', email: '<EMAIL>' });

console.log('All users:', userService.getAllUsers());
console.log('User 1:', userService.getUser(user1.id));

// Update user
userService.updateUser(user1.id, { age: 30 });

// Delete user
userService.deleteUser(user2.id);
```

15. What is the difference between `exports` and `module.exports`?

`module.exports` is the actual object that gets exported, while `exports` is just a reference to `module.exports`.

```javascript
// Initially: exports === module.exports === {}

// Method 1: Using exports (adds properties)
exports.name = 'John';
exports.greet = function() {
    return 'Hello';
};
// Result: module.exports = { name: 'John', greet: function() {...} }

// Method 2: Using module.exports (replaces entire object)
module.exports = {
    name: 'John',
    greet: function() {
        return 'Hello';
    }
};

// Method 3: Exporting a class or function
module.exports = class User {
    constructor(name) {
        this.name = name;
    }
};

// ❌ This breaks the reference and won't work
exports = {
    name: 'John',
    greet: function() {
        return 'Hello';
    }
};
// exports now points to a different object, but module.exports is still {}

// ✅ This works
exports.name = 'John';
module.exports.greet = function() {
    return 'Hello';
};
// Both modify the same object
```

Best practices:
- Use `module.exports` when exporting a single item (class, function)
- Use `exports` when exporting multiple properties
- Don't mix both approaches in the same file
- Be consistent across your project

16. What is middleware in Node.js?

Middleware functions are functions that execute during the request-response cycle. They have access to request, response objects and the next middleware function.

```javascript
const express = require('express');
const app = express();

// Built-in middleware
app.use(express.json()); // Parse JSON bodies
app.use(express.static('public')); // Serve static files

// Custom middleware
const logger = (req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next(); // Call next middleware
};

const authenticate = (req, res, next) => {
    const token = req.headers.authorization;
    if (!token) {
        return res.status(401).json({ error: 'No token provided' });
    }

    // Verify token logic here
    req.user = { id: 1, name: 'John' }; // Add user to request
    next();
};

const validateUser = (req, res, next) => {
    const { name, email } = req.body;
    if (!name || !email) {
        return res.status(400).json({ error: 'Name and email required' });
    }
    next();
};

// Apply middleware
app.use(logger); // Global middleware

// Route-specific middleware
app.post('/users', authenticate, validateUser, (req, res) => {
    res.json({ message: 'User created', user: req.body });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});
```

Types of middleware:
- Application-level: `app.use()`
- Router-level: `router.use()`
- Error-handling: `(err, req, res, next) => {}`
- Built-in: `express.json()`, `express.static()`
- Third-party: `cors`, `helmet`, `morgan`

17. How does the `require` function work internally?

The `require` function follows these steps:

1. Resolving: Find the module file
2. Loading: Read the file content
3. Wrapping: Wrap in a function
4. Evaluation: Execute the wrapped function
5. Caching: Store the result

```javascript
// Simplified require implementation
function require(id) {
    // 1. Resolve the module path
    const filename = Module._resolveFilename(id);

    // 2. Check cache
    if (Module._cache[filename]) {
        return Module._cache[filename].exports;
    }

    // 3. Create new module
    const module = new Module(filename);
    Module._cache[filename] = module;

    // 4. Load and compile
    module.load(filename);

    // 5. Return exports
    return module.exports;
}

// Module wrapping
(function(exports, require, module, __filename, __dirname) {
    // Your module code here
    const fs = require('fs');
    module.exports = { hello: 'world' };
});
```

Module resolution order:
1. Core modules (fs, http, etc.)
2. File modules (./file.js, ../file.js)
3. Folder modules (./folder/index.js)
4. node_modules (./node_modules, ../node_modules, etc.)

```javascript
// Resolution examples
require('fs');           // Core module
require('./utils');      // Local file: ./utils.js
require('./lib');        // Local folder: ./lib/index.js
require('express');      // node_modules: ./node_modules/express/
require('@babel/core');  // Scoped package
```

18. What is the difference between relative and absolute paths in Node.js?

Relative paths: Relative to the current file location
Absolute paths: Full path from root directory

```javascript
// File structure:
// /project
//   /src
//     /utils
//       helper.js
//     app.js
//   /config
//     database.js

// In /src/app.js
const helper = require('./utils/helper');        // Relative path
const config = require('../config/database');    // Relative path
const path = require('path');                     // Core module

// Absolute paths (not recommended for local modules)
const helper2 = require('/project/src/utils/helper'); // Absolute path

// Using __dirname and path.join for absolute paths
const configPath = path.join(__dirname, '..', 'config', 'database');
const config2 = require(configPath);

// Global variables available in modules
console.log(__filename); // Full path to current file
console.log(__dirname);  // Directory of current file
console.log(process.cwd()); // Current working directory
```

Best practices:
- Use relative paths for local modules
- Use `path.join()` for cross-platform compatibility
- Avoid hardcoded absolute paths
- Use `__dirname` for file operations

19. How do you resolve module paths in Node.js?

Node.js uses a specific algorithm to resolve module paths:

```javascript
const path = require('path');

// Module resolution process
console.log(require.resolve('express')); // Full path to express module
console.log(require.resolve('./utils'));  // Full path to local module

// Manual path resolution
const modulePath = path.resolve(__dirname, 'utils', 'helper.js');
console.log(modulePath);

// Check if module exists
try {
    require.resolve('some-module');
    console.log('Module exists');
} catch (err) {
    console.log('Module not found');
}

// Module paths
console.log(module.paths); // Array of paths Node.js searches for modules

// Custom module resolution
const Module = require('module');
const originalResolveFilename = Module._resolveFilename;

Module._resolveFilename = function(request, parent) {
    console.log('Resolving:', request);
    return originalResolveFilename(request, parent);
};
```

Resolution algorithm:
1. If core module → return core module
2. If starts with '/' → absolute path
3. If starts with './' or '../' → relative path
4. Otherwise → look in node_modules

20. What is the difference between global objects in Node.js and browser?

Node.js global objects:
```javascript
// Node.js specific globals
console.log(global);        // Global namespace
console.log(process);       // Process information
console.log(Buffer);        // Binary data handling
console.log(__filename);    // Current file path
console.log(__dirname);     // Current directory
console.log(require);       // Module loader
console.log(module);        // Current module
console.log(exports);       // Module exports

// Process object
console.log(process.version);    // Node.js version
console.log(process.platform);  // Operating system
console.log(process.argv);       // Command line arguments
console.log(process.env);        // Environment variables
```

Browser global objects:
```javascript
// Browser specific globals (not available in Node.js)
// console.log(window);        // Window object
// console.log(document);      // DOM
// console.log(navigator);     // Browser info
// console.log(location);      // URL info
// console.log(localStorage);  // Local storage
// console.log(sessionStorage); // Session storage
// console.log(fetch);         // Fetch API
```

Shared globals:
```javascript
// Available in both environments
console.log(console);       // Console object
console.log(setTimeout);    // Timer functions
console.log(setInterval);
console.log(clearTimeout);
console.log(clearInterval);
console.log(JSON);          // JSON object
console.log(parseInt);      // Global functions
console.log(parseFloat);
```

## 3. File System & Streams

21. How do you read and write files in Node.js?

```javascript
const fs = require('fs');
const fsPromises = require('fs').promises;

// Synchronous file operations (blocking)
try {
    const data = fs.readFileSync('input.txt', 'utf8');
    console.log('File content:', data);

    fs.writeFileSync('output.txt', 'Hello World', 'utf8');
    console.log('File written successfully');
} catch (error) {
    console.error('Error:', error);
}

// Asynchronous file operations (non-blocking)
fs.readFile('input.txt', 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading file:', err);
        return;
    }
    console.log('File content:', data);
});

fs.writeFile('output.txt', 'Hello World', 'utf8', (err) => {
    if (err) {
        console.error('Error writing file:', err);
        return;
    }
    console.log('File written successfully');
});

// Promise-based file operations
async function fileOperations() {
    try {
        const data = await fsPromises.readFile('input.txt', 'utf8');
        console.log('File content:', data);

        await fsPromises.writeFile('output.txt', 'Hello World', 'utf8');
        console.log('File written successfully');

        // Append to file
        await fsPromises.appendFile('output.txt', '\nAppended text', 'utf8');

        // Check if file exists
        await fsPromises.access('input.txt');
        console.log('File exists');

        // Get file stats
        const stats = await fsPromises.stat('input.txt');
        console.log('File size:', stats.size);
        console.log('Is file:', stats.isFile());
        console.log('Is directory:', stats.isDirectory());

    } catch (error) {
        console.error('Error:', error);
    }
}
```

22. Difference between synchronous and asynchronous file operations.

Synchronous (Blocking):
```javascript
const fs = require('fs');

console.log('Start');

// Blocks execution until file is read
const data = fs.readFileSync('large-file.txt', 'utf8');
console.log('File read complete');

console.log('End');
// Output: Start, File read complete, End
```

Asynchronous (Non-blocking):
```javascript
const fs = require('fs');

console.log('Start');

// Doesn't block execution
fs.readFile('large-file.txt', 'utf8', (err, data) => {
    if (err) throw err;
    console.log('File read complete');
});

console.log('End');
// Output: Start, End, File read complete
```

Performance comparison:
```javascript
const fs = require('fs');

// Synchronous - processes files one by one
function readFilesSync(files) {
    const results = [];
    for (const file of files) {
        const data = fs.readFileSync(file, 'utf8');
        results.push(data);
    }
    return results;
}

// Asynchronous - processes files concurrently
async function readFilesAsync(files) {
    const promises = files.map(file =>
        fs.promises.readFile(file, 'utf8')
    );
    return Promise.all(promises);
}
```

23. What is a stream in Node.js?

Streams are objects that handle reading/writing data piece by piece (chunks) instead of loading everything into memory at once.

Benefits:
- Memory efficient for large files
- Time efficient (start processing before all data is available)
- Composable (can be piped together)

```javascript
const fs = require('fs');
const { Transform } = require('stream');

// Reading large file with stream
const readStream = fs.createReadStream('large-file.txt', {
    encoding: 'utf8',
    highWaterMark: 1024 // 1KB chunks
});

readStream.on('data', (chunk) => {
    console.log('Received chunk:', chunk.length, 'bytes');
});

readStream.on('end', () => {
    console.log('File reading completed');
});

readStream.on('error', (err) => {
    console.error('Error:', err);
});

// Writing with stream
const writeStream = fs.createWriteStream('output.txt');

writeStream.write('Hello ');
writeStream.write('World ');
writeStream.end('!\n');

writeStream.on('finish', () => {
    console.log('Writing completed');
});
```

24. Different types of streams in Node.js.

1. Readable Streams: Read data from source
2. Writable Streams: Write data to destination
3. Duplex Streams: Both readable and writable
4. Transform Streams: Modify data as it passes through

```javascript
const { Readable, Writable, Transform, Duplex } = require('stream');

// Custom Readable Stream
class NumberStream extends Readable {
    constructor(max) {
        super();
        this.current = 0;
        this.max = max;
    }

    _read() {
        if (this.current < this.max) {
            this.push(`${this.current++}\n`);
        } else {
            this.push(null); // End stream
        }
    }
}

// Custom Writable Stream
class LogStream extends Writable {
    _write(chunk, encoding, callback) {
        console.log(`LOG: ${chunk.toString().trim()}`);
        callback();
    }
}

// Custom Transform Stream
class UpperCaseTransform extends Transform {
    _transform(chunk, encoding, callback) {
        this.push(chunk.toString().toUpperCase());
        callback();
    }
}

// Usage
const numberStream = new NumberStream(5);
const upperCaseTransform = new UpperCaseTransform();
const logStream = new LogStream();

numberStream
    .pipe(upperCaseTransform)
    .pipe(logStream);
```

25. What is the difference between `readFile` and `createReadStream`?

readFile: Loads entire file into memory
```javascript
const fs = require('fs');

// Loads entire file into memory
fs.readFile('large-file.txt', 'utf8', (err, data) => {
    if (err) throw err;
    console.log('File size:', data.length);
    // Entire file is in memory as 'data'
});

// Memory usage: File size
// Good for: Small files, when you need entire content at once
```

createReadStream: Reads file in chunks
```javascript
const fs = require('fs');

// Reads file in chunks
const stream = fs.createReadStream('large-file.txt', {
    encoding: 'utf8',
    highWaterMark: 1024 // 1KB chunks
});

let totalSize = 0;

stream.on('data', (chunk) => {
    totalSize += chunk.length;
    console.log('Chunk size:', chunk.length);
    // Process chunk immediately
});

stream.on('end', () => {
    console.log('Total size:', totalSize);
});

// Memory usage: Chunk size (1KB)
// Good for: Large files, streaming processing
```

Performance comparison:
```javascript
// For a 1GB file:
// readFile: Uses 1GB RAM, waits for entire file
// createReadStream: Uses ~1KB RAM, starts processing immediately
```

26. What is `pipe()` in Node.js streams?

pipe() connects readable stream output to writable stream input, automatically handling data flow, backpressure, and error handling.

```javascript
const fs = require('fs');
const zlib = require('zlib');
const { Transform } = require('stream');

// Basic pipe
fs.createReadStream('input.txt')
  .pipe(fs.createWriteStream('output.txt'));

// Chaining multiple pipes
fs.createReadStream('input.txt')
  .pipe(zlib.createGzip())                    // Compress
  .pipe(fs.createWriteStream('output.txt.gz')); // Write compressed

// Transform stream in pipe chain
const upperCaseTransform = new Transform({
    transform(chunk, encoding, callback) {
        callback(null, chunk.toString().toUpperCase());
    }
});

fs.createReadStream('input.txt')
  .pipe(upperCaseTransform)
  .pipe(fs.createWriteStream('output.txt'));

// Error handling with pipe
const pipeline = require('stream').pipeline;

pipeline(
    fs.createReadStream('input.txt'),
    upperCaseTransform,
    fs.createWriteStream('output.txt'),
    (err) => {
        if (err) {
            console.error('Pipeline failed:', err);
        } else {
            console.log('Pipeline succeeded');
        }
    }
);
```

Benefits of pipe():
- Automatic backpressure handling
- Memory efficient
- Clean, readable code
- Automatic error propagation (with pipeline)

27. How do you handle large file uploads in Node.js?

```javascript
const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const app = express();

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    },
    fileFilter: (req, file, cb) => {
        // Allow only specific file types
        if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type'));
        }
    }
});

// Single file upload
app.post('/upload', upload.single('file'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
    }

    res.json({
        message: 'File uploaded successfully',
        filename: req.file.filename,
        size: req.file.size
    });
});

// Multiple file upload
app.post('/upload-multiple', upload.array('files', 5), (req, res) => {
    res.json({
        message: 'Files uploaded successfully',
        files: req.files.map(file => ({
            filename: file.filename,
            size: file.size
        }))
    });
});

// Streaming upload without multer
app.post('/upload-stream', (req, res) => {
    const filename = `upload-${Date.now()}.bin`;
    const writeStream = fs.createWriteStream(path.join('uploads', filename));

    req.pipe(writeStream);

    writeStream.on('finish', () => {
        res.json({ message: 'File uploaded successfully', filename });
    });

    writeStream.on('error', (err) => {
        res.status(500).json({ error: 'Upload failed' });
    });
});

// Progress tracking
app.post('/upload-progress', (req, res) => {
    let uploadedBytes = 0;
    const contentLength = parseInt(req.headers['content-length']);

    req.on('data', (chunk) => {
        uploadedBytes += chunk.length;
        const progress = (uploadedBytes / contentLength) * 100;
        console.log(`Upload progress: ${progress.toFixed(2)}%`);
    });

    req.on('end', () => {
        res.json({ message: 'Upload complete' });
    });
});
```

28. What are buffers in Node.js?

Buffers handle binary data in Node.js. They represent fixed-size chunks of memory allocated outside the V8 heap.

```javascript
// Creating buffers
const buf1 = Buffer.alloc(10);           // 10 bytes, filled with zeros
const buf2 = Buffer.allocUnsafe(10);     // 10 bytes, uninitialized (faster)
const buf3 = Buffer.from('hello');       // From string
const buf4 = Buffer.from([1, 2, 3, 4]);  // From array

console.log(buf1); // <Buffer 00 00 00 00 00 00 00 00 00 00>
console.log(buf3); // <Buffer 68 65 6c 6c 6f>

// Buffer operations
const buffer = Buffer.from('Hello World');

console.log(buffer.length);           // 11
console.log(buffer.toString());       // 'Hello World'
console.log(buffer.toString('hex'));  // '48656c6c6f20576f726c64'
console.log(buffer.toString('base64')); // 'SGVsbG8gV29ybGQ='

// Writing to buffer
const buf = Buffer.alloc(20);
buf.write('Hello', 0, 'utf8');
buf.write(' World', 5, 'utf8');
console.log(buf.toString()); // 'Hello World'

// Buffer concatenation
const buf1 = Buffer.from('Hello ');
const buf2 = Buffer.from('World');
const combined = Buffer.concat([buf1, buf2]);
console.log(combined.toString()); // 'Hello World'

// Buffer comparison
const bufA = Buffer.from('abc');
const bufB = Buffer.from('abc');
console.log(bufA.equals(bufB)); // true
console.log(Buffer.compare(bufA, bufB)); // 0 (equal)
```

29. Difference between buffer and stream.

Buffer: Fixed-size binary data container
Stream: Continuous flow of data

```javascript
// Buffer - all data in memory at once
const fs = require('fs');

// Reading entire file into buffer
const buffer = fs.readFileSync('file.txt');
console.log('Buffer size:', buffer.length);
console.log('Buffer content:', buffer.toString());

// Stream - data flows in chunks
const stream = fs.createReadStream('file.txt');

stream.on('data', (chunk) => {
    console.log('Chunk (Buffer):', chunk);
    console.log('Chunk size:', chunk.length);
    console.log('Chunk content:', chunk.toString());
});

// Buffer vs Stream for large files
// Buffer: Loads 1GB file → Uses 1GB RAM
// Stream: Processes 1GB file in 64KB chunks → Uses 64KB RAM
```

Use cases:
- Buffer: Small files, binary operations, encoding/decoding
- Stream: Large files, real-time data, network operations

30. How do you delete or rename files in Node.js?

```javascript
const fs = require('fs').promises;
const path = require('path');

async function fileOperations() {
    try {
        // Delete file
        await fs.unlink('file-to-delete.txt');
        console.log('File deleted successfully');

        // Rename file
        await fs.rename('old-name.txt', 'new-name.txt');
        console.log('File renamed successfully');

        // Move file (rename with different directory)
        await fs.rename('file.txt', path.join('new-folder', 'file.txt'));
        console.log('File moved successfully');

        // Delete directory (must be empty)
        await fs.rmdir('empty-directory');
        console.log('Directory deleted');

        // Delete directory with contents (Node.js 14.14+)
        await fs.rm('directory-with-files', { recursive: true, force: true });
        console.log('Directory and contents deleted');

        // Copy file
        await fs.copyFile('source.txt', 'destination.txt');
        console.log('File copied successfully');

    } catch (error) {
        console.error('Error:', error);
    }
}

// Synchronous versions
try {
    fs.unlinkSync('file.txt');        // Delete file
    fs.renameSync('old.txt', 'new.txt'); // Rename file
    fs.rmdirSync('directory');         // Delete empty directory
} catch (error) {
    console.error('Error:', error);
}

// Check if file exists before operations
async function safeDelete(filename) {
    try {
        await fs.access(filename);
        await fs.unlink(filename);
        console.log(`${filename} deleted successfully`);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.log(`${filename} does not exist`);
        } else {
            console.error('Error deleting file:', error);
        }
    }
}
```

## 4. Events & Async Handling

31. What is the EventEmitter class in Node.js?

EventEmitter is a core Node.js class that enables objects to emit and listen for events, implementing the observer pattern.

```javascript
const EventEmitter = require('events');

// Create an EventEmitter instance
const emitter = new EventEmitter();

// Listen for events
emitter.on('message', (data) => {
    console.log('Received message:', data);
});

emitter.on('error', (error) => {
    console.error('Error occurred:', error);
});

// Emit events
emitter.emit('message', 'Hello World!');
emitter.emit('error', new Error('Something went wrong'));

// One-time listener
emitter.once('startup', () => {
    console.log('Application started');
});

emitter.emit('startup'); // Fires
emitter.emit('startup'); // Doesn't fire (already removed)

// Remove listeners
const handler = (data) => console.log(data);
emitter.on('test', handler);
emitter.removeListener('test', handler);

// Remove all listeners for an event
emitter.removeAllListeners('message');
```

32. How do you create and listen to custom events in Node.js?

```javascript
const EventEmitter = require('events');

// Custom class extending EventEmitter
class UserService extends EventEmitter {
    constructor() {
        super();
        this.users = [];
    }

    addUser(user) {
        this.users.push(user);

        // Emit custom events
        this.emit('userAdded', user);
        this.emit('userCount', this.users.length);

        if (this.users.length === 1) {
            this.emit('firstUser', user);
        }
    }

    removeUser(userId) {
        const userIndex = this.users.findIndex(u => u.id === userId);
        if (userIndex !== -1) {
            const removedUser = this.users.splice(userIndex, 1)[0];
            this.emit('userRemoved', removedUser);
            this.emit('userCount', this.users.length);
        }
    }
}

// Usage
const userService = new UserService();

// Listen to custom events
userService.on('userAdded', (user) => {
    console.log('New user added:', user.name);
    // Send welcome email
    // Update analytics
});

userService.on('userRemoved', (user) => {
    console.log('User removed:', user.name);
    // Cleanup user data
    // Send goodbye email
});

userService.on('userCount', (count) => {
    console.log('Total users:', count);
});

userService.once('firstUser', (user) => {
    console.log('First user registered:', user.name);
    // Special first user bonus
});

// Add users
userService.addUser({ id: 1, name: 'John', email: '<EMAIL>' });
userService.addUser({ id: 2, name: 'Jane', email: '<EMAIL>' });

// Remove user
userService.removeUser(1);
```

33. What is the difference between callbacks, promises, and async/await in Node.js?

Callbacks (traditional approach):
```javascript
const fs = require('fs');

// Callback hell
fs.readFile('file1.txt', 'utf8', (err, data1) => {
    if (err) {
        console.error('Error reading file1:', err);
        return;
    }

    fs.readFile('file2.txt', 'utf8', (err, data2) => {
        if (err) {
            console.error('Error reading file2:', err);
            return;
        }

        fs.writeFile('combined.txt', data1 + data2, (err) => {
            if (err) {
                console.error('Error writing file:', err);
                return;
            }
            console.log('Files combined successfully');
        });
    });
});
```

Promises (ES6):
```javascript
const fs = require('fs').promises;

// Promise chaining
fs.readFile('file1.txt', 'utf8')
    .then(data1 => {
        return fs.readFile('file2.txt', 'utf8')
            .then(data2 => ({ data1, data2 }));
    })
    .then(({ data1, data2 }) => {
        return fs.writeFile('combined.txt', data1 + data2);
    })
    .then(() => {
        console.log('Files combined successfully');
    })
    .catch(error => {
        console.error('Error:', error);
    });

// Better with Promise.all
Promise.all([
    fs.readFile('file1.txt', 'utf8'),
    fs.readFile('file2.txt', 'utf8')
])
.then(([data1, data2]) => {
    return fs.writeFile('combined.txt', data1 + data2);
})
.then(() => {
    console.log('Files combined successfully');
})
.catch(error => {
    console.error('Error:', error);
});
```

Async/Await (ES2017):
```javascript
const fs = require('fs').promises;

async function combineFiles() {
    try {
        // Sequential reading
        const data1 = await fs.readFile('file1.txt', 'utf8');
        const data2 = await fs.readFile('file2.txt', 'utf8');

        // Or parallel reading
        const [data1, data2] = await Promise.all([
            fs.readFile('file1.txt', 'utf8'),
            fs.readFile('file2.txt', 'utf8')
        ]);

        await fs.writeFile('combined.txt', data1 + data2);
        console.log('Files combined successfully');

    } catch (error) {
        console.error('Error:', error);
    }
}

combineFiles();
```

34. How do you handle errors in asynchronous code?

Callback error handling:
```javascript
const fs = require('fs');

fs.readFile('file.txt', 'utf8', (err, data) => {
    if (err) {
        if (err.code === 'ENOENT') {
            console.error('File not found');
        } else if (err.code === 'EACCES') {
            console.error('Permission denied');
        } else {
            console.error('Unknown error:', err);
        }
        return;
    }

    console.log('File content:', data);
});
```

Promise error handling:
```javascript
const fs = require('fs').promises;

fs.readFile('file.txt', 'utf8')
    .then(data => {
        console.log('File content:', data);
    })
    .catch(error => {
        console.error('Error reading file:', error);
    });

// Multiple operations with error handling
Promise.all([
    fs.readFile('file1.txt', 'utf8'),
    fs.readFile('file2.txt', 'utf8')
])
.then(([data1, data2]) => {
    console.log('Both files read successfully');
})
.catch(error => {
    console.error('Error reading files:', error);
});
```

Async/await error handling:
```javascript
const fs = require('fs').promises;

async function readFileWithErrorHandling() {
    try {
        const data = await fs.readFile('file.txt', 'utf8');
        console.log('File content:', data);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.error('File not found');
        } else {
            console.error('Error reading file:', error);
        }
    }
}

// Global error handling
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
```

35. What is process.nextTick() in Node.js?

process.nextTick() schedules a callback to be invoked in the next iteration of the event loop, before any other I/O events.

```javascript
console.log('Start');

process.nextTick(() => {
    console.log('Next tick callback');
});

setTimeout(() => {
    console.log('Timer callback');
}, 0);

setImmediate(() => {
    console.log('Immediate callback');
});

console.log('End');

// Output: Start, End, Next tick callback, Immediate callback, Timer callback
```

Use cases and examples:
```javascript
// API consistency
function asyncFunction(callback) {
    if (typeof callback !== 'function') {
        // Use nextTick to make error handling consistent
        process.nextTick(() => {
            throw new TypeError('Callback must be a function');
        });
        return;
    }

    // Ensure callback is always async
    process.nextTick(callback);
}

// Error handling
function readFileAsync(filename, callback) {
    if (!filename) {
        process.nextTick(() => {
            callback(new Error('Filename is required'));
        });
        return;
    }

    // Actual file reading logic
    fs.readFile(filename, callback);
}

// Event emitter initialization
class MyEmitter extends EventEmitter {
    constructor() {
        super();

        // Emit event after constructor completes
        process.nextTick(() => {
            this.emit('ready');
        });
    }
}

const emitter = new MyEmitter();
emitter.on('ready', () => {
    console.log('Emitter is ready');
});
```

36. Difference between `setImmediate()` and `process.nextTick()`.

Execution order and priority:

```javascript
console.log('1: Start');

setTimeout(() => console.log('2: Timer'), 0);

process.nextTick(() => console.log('3: Next Tick 1'));

setImmediate(() => console.log('4: Immediate 1'));

process.nextTick(() => console.log('5: Next Tick 2'));

Promise.resolve().then(() => console.log('6: Promise'));

setImmediate(() => console.log('7: Immediate 2'));

console.log('8: End');

// Output: 1, 8, 3, 5, 6, 4, 7, 2
```

Key differences:
- process.nextTick(): Highest priority, executes before any other async operation
- setImmediate(): Executes in the check phase of the event loop
- nextTick can cause starvation if used recursively
- setImmediate is safer for recursive calls

```javascript
// Potential starvation with nextTick
function recursiveNextTick() {
    process.nextTick(recursiveNextTick);
    console.log('This will block other operations');
}

// Safer with setImmediate
function recursiveImmediate() {
    setImmediate(recursiveImmediate);
    console.log('This allows other operations');
}
```

37. What are microtasks and macrotasks in Node.js?

Microtasks (higher priority):
- process.nextTick()
- Promise callbacks (.then, .catch, .finally)
- queueMicrotask()

Macrotasks (lower priority):
- setTimeout, setInterval
- setImmediate
- I/O operations
- HTTP requests

```javascript
console.log('1: Synchronous');

// Macrotasks
setTimeout(() => console.log('2: setTimeout'), 0);
setImmediate(() => console.log('3: setImmediate'));

// Microtasks
process.nextTick(() => console.log('4: nextTick'));
Promise.resolve().then(() => console.log('5: Promise'));
queueMicrotask(() => console.log('6: queueMicrotask'));

console.log('7: Synchronous');

// Output: 1, 7, 4, 5, 6, 3, 2
```

Event loop processing order:
1. Execute all synchronous code
2. Process all microtasks (nextTick queue, then microtask queue)
3. Process one macrotask
4. Process all microtasks again
5. Repeat steps 3-4

38. How does Node.js handle concurrency despite being single-threaded?

Node.js achieves concurrency through:
1. Event-driven architecture
2. Non-blocking I/O operations
3. Thread pool for I/O operations
4. Event loop for coordination

```javascript
const fs = require('fs');
const http = require('http');

// Concurrent file operations
console.log('Starting concurrent operations...');

const startTime = Date.now();

// These operations run concurrently
fs.readFile('file1.txt', (err, data1) => {
    console.log(`File 1 read in ${Date.now() - startTime}ms`);
});

fs.readFile('file2.txt', (err, data2) => {
    console.log(`File 2 read in ${Date.now() - startTime}ms`);
});

fs.readFile('file3.txt', (err, data3) => {
    console.log(`File 3 read in ${Date.now() - startTime}ms`);
});

// HTTP server handling multiple requests concurrently
const server = http.createServer((req, res) => {
    // Each request is handled concurrently
    console.log(`Request received at ${Date.now()}`);

    // Simulate async operation
    setTimeout(() => {
        res.writeHead(200, { 'Content-Type': 'text/plain' });
        res.end('Hello World');
    }, 1000);
});

server.listen(3000, () => {
    console.log('Server listening on port 3000');
});
```

39. What is clustering in Node.js?

Clustering allows you to create multiple Node.js processes to handle load across multiple CPU cores.

```javascript
const cluster = require('cluster');
const http = require('http');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
    console.log(`Master ${process.pid} is running`);

    // Fork workers
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }

    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} died`);
        // Restart worker
        cluster.fork();
    });

} else {
    // Workers can share any TCP port
    http.createServer((req, res) => {
        res.writeHead(200);
        res.end(`Hello from worker ${process.pid}\n`);
    }).listen(3000);

    console.log(`Worker ${process.pid} started`);
}

// Advanced clustering with PM2
// pm2 start app.js -i max  // Start with maximum CPU cores
// pm2 start app.js -i 4    // Start with 4 instances
```

40. What is worker_threads module in Node.js?

worker_threads allows running JavaScript in parallel threads for CPU-intensive tasks.

```javascript
// main.js
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

if (isMainThread) {
    // Main thread
    console.log('Main thread');

    const worker = new Worker(__filename, {
        workerData: { start: 0, end: 1000000 }
    });

    worker.on('message', (result) => {
        console.log('Result from worker:', result);
    });

    worker.on('error', (error) => {
        console.error('Worker error:', error);
    });

    worker.on('exit', (code) => {
        if (code !== 0) {
            console.error(`Worker stopped with exit code ${code}`);
        }
    });

} else {
    // Worker thread
    console.log('Worker thread');

    const { start, end } = workerData;

    // CPU-intensive calculation
    let sum = 0;
    for (let i = start; i < end; i++) {
        sum += i;
    }

    // Send result back to main thread
    parentPort.postMessage(sum);
}
```

Multiple workers example:
```javascript
const { Worker } = require('worker_threads');
const path = require('path');

async function runWorker(workerData) {
    return new Promise((resolve, reject) => {
        const worker = new Worker(path.join(__dirname, 'worker.js'), {
            workerData
        });

        worker.on('message', resolve);
        worker.on('error', reject);
        worker.on('exit', (code) => {
            if (code !== 0) {
                reject(new Error(`Worker stopped with exit code ${code}`));
            }
        });
    });
}

async function main() {
    const tasks = [
        { start: 0, end: 250000 },
        { start: 250000, end: 500000 },
        { start: 500000, end: 750000 },
        { start: 750000, end: 1000000 }
    ];

    try {
        const results = await Promise.all(
            tasks.map(task => runWorker(task))
        );

        const total = results.reduce((sum, result) => sum + result, 0);
        console.log('Total sum:', total);
    } catch (error) {
        console.error('Error:', error);
    }
}

main();
```

## 5. Networking, Security & Deployment

41. How do you create a basic HTTP server in Node.js?

```javascript
const http = require('http');
const url = require('url');

// Basic HTTP server
const server = http.createServer((req, res) => {
    // Parse URL
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle different routes
    if (path === '/' && method === 'GET') {
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end('<h1>Welcome to Node.js Server</h1>');

    } else if (path === '/api/users' && method === 'GET') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ users: ['John', 'Jane'] }));

    } else if (path === '/api/users' && method === 'POST') {
        let body = '';

        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            try {
                const userData = JSON.parse(body);
                res.writeHead(201, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ message: 'User created', user: userData }));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
        });

    } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not Found' }));
    }
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

// Handle server errors
server.on('error', (error) => {
    console.error('Server error:', error);
});
```

42. What is the difference between HTTP and HTTPS in Node.js?

HTTP (Hypertext Transfer Protocol):
```javascript
const http = require('http');

const httpServer = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('HTTP Server Response');
});

httpServer.listen(3000, () => {
    console.log('HTTP Server running on port 3000');
});
```

HTTPS (HTTP Secure):
```javascript
const https = require('https');
const fs = require('fs');

// SSL certificate options
const options = {
    key: fs.readFileSync('private-key.pem'),
    cert: fs.readFileSync('certificate.pem')
};

const httpsServer = https.createServer(options, (req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('HTTPS Server Response');
});

httpsServer.listen(443, () => {
    console.log('HTTPS Server running on port 443');
});

// Self-signed certificate for development
// openssl req -x509 -newkey rsa:2048 -keyout private-key.pem -out certificate.pem -days 365 -nodes
```

Key differences:
- HTTP: Plain text, port 80, no encryption
- HTTPS: Encrypted, port 443, SSL/TLS encryption
- HTTPS requires SSL certificates
- HTTPS provides data integrity and authentication

43. How do you implement JWT authentication in Node.js?

```javascript
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const express = require('express');

const app = express();
app.use(express.json());

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const users = []; // In production, use a database

// Register user
app.post('/register', async (req, res) => {
    try {
        const { username, password } = req.body;

        // Check if user exists
        const existingUser = users.find(u => u.username === username);
        if (existingUser) {
            return res.status(400).json({ error: 'User already exists' });
        }

        // Hash password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Save user
        const user = {
            id: users.length + 1,
            username,
            password: hashedPassword
        };
        users.push(user);

        res.status(201).json({ message: 'User registered successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Registration failed' });
    }
});

// Login user
app.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // Find user
        const user = users.find(u => u.username === username);
        if (!user) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Generate JWT token
        const token = jwt.sign(
            { userId: user.id, username: user.username },
            JWT_SECRET,
            { expiresIn: '24h' }
        );

        res.json({ token, user: { id: user.id, username: user.username } });
    } catch (error) {
        res.status(500).json({ error: 'Login failed' });
    }
});

// JWT middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
};

// Protected route
app.get('/profile', authenticateToken, (req, res) => {
    res.json({ message: 'Protected data', user: req.user });
});

// Refresh token
app.post('/refresh', authenticateToken, (req, res) => {
    const newToken = jwt.sign(
        { userId: req.user.userId, username: req.user.username },
        JWT_SECRET,
        { expiresIn: '24h' }
    );

    res.json({ token: newToken });
});

app.listen(3000, () => {
    console.log('Server running on port 3000');
});
```

44. What is CORS in Node.js and how do you enable it?

CORS (Cross-Origin Resource Sharing) allows web pages to make requests to a different domain than the one serving the page.

```javascript
const express = require('express');
const cors = require('cors');

const app = express();

// Method 1: Using cors middleware
app.use(cors()); // Enable CORS for all routes

// Method 2: Custom CORS configuration
const corsOptions = {
    origin: ['http://localhost:3000', 'https://myapp.com'],
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true // Allow cookies
};

app.use(cors(corsOptions));

// Method 3: Manual CORS headers
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Method 4: Route-specific CORS
app.get('/api/public', cors(), (req, res) => {
    res.json({ message: 'Public endpoint with CORS' });
});

// Method 5: Dynamic CORS
const dynamicCors = (req, res, next) => {
    const allowedOrigins = ['http://localhost:3000', 'https://myapp.com'];
    const origin = req.headers.origin;

    if (allowedOrigins.includes(origin)) {
        res.setHeader('Access-Control-Allow-Origin', origin);
    }

    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
};

app.use('/api', dynamicCors);
```

45. How do you handle environment variables in Node.js?

```javascript
// Using process.env
console.log('Node environment:', process.env.NODE_ENV);
console.log('Port:', process.env.PORT || 3000);
console.log('Database URL:', process.env.DATABASE_URL);

// .env file with dotenv package
require('dotenv').config();

// .env file content:
// NODE_ENV=development
// PORT=3000
// DATABASE_URL=mongodb://localhost:27017/myapp
// JWT_SECRET=your-secret-key
// API_KEY=your-api-key

// Environment-specific configuration
const config = {
    development: {
        port: process.env.PORT || 3000,
        database: process.env.DATABASE_URL || 'mongodb://localhost:27017/myapp-dev',
        logLevel: 'debug'
    },
    production: {
        port: process.env.PORT || 80,
        database: process.env.DATABASE_URL,
        logLevel: 'error'
    },
    test: {
        port: process.env.PORT || 3001,
        database: process.env.TEST_DATABASE_URL || 'mongodb://localhost:27017/myapp-test',
        logLevel: 'silent'
    }
};

const currentConfig = config[process.env.NODE_ENV || 'development'];

// Validation
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];

requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
        console.error(`Missing required environment variable: ${envVar}`);
        process.exit(1);
    }
});

// Type conversion
const PORT = parseInt(process.env.PORT) || 3000;
const ENABLE_LOGGING = process.env.ENABLE_LOGGING === 'true';
const MAX_CONNECTIONS = parseInt(process.env.MAX_CONNECTIONS) || 100;
```

46-50. [Remaining questions covering rate limiting, security, deployment, and best practices]

Rate limiting implementation:
```javascript
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
});

app.use('/api/', limiter);
```

Security best practices:
- Use HTTPS in production
- Validate and sanitize input data
- Implement proper authentication and authorization
- Use security headers (helmet.js)
- Keep dependencies updated
- Use environment variables for secrets
- Implement rate limiting
- Log security events
- Use CORS appropriately
- Validate file uploads

Deployment considerations:
- Use process managers (PM2, Forever)
- Set up reverse proxy (Nginx)
- Configure SSL certificates
- Set up monitoring and logging
- Use clustering for scalability
- Implement health checks
- Set up CI/CD pipelines
- Use containerization (Docker)
- Configure environment variables
- Set up database connections properly
